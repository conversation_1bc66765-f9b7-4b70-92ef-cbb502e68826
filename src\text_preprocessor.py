"""
文本预处理模块
负责文本的预处理，包括停用词处理、正则化分词和金融领域词典支持
提供高效的文本清洗和分词功能，优化金融研报文本处理
"""

import jieba
import re
import os
import time
from collections import Counter


def load_stopwords(stopwords_path):
    """
    加载停用词表，如果文件不存在则使用内置的基本停用词

    参数:
        stopwords_path (str): 停用词文件路径

    返回:
        set: 停用词集合
    """
    # 基本停用词集合，如果文件加载失败将使用这个
    basic_stopwords = {
        '的', '了', '和', '是', '就', '都', '而', '及', '与', '着', '或', '等', '之', '以',
        '对', '于', '中', '为', '来', '由', '从', '被', '向', '将', '但', '且', '因', '所',
        '如', '若', '则', '能', '可', '要', '会', '这', '那', '有', '在', '我', '他', '它',
        '你', '您', '她', '们', '个', '某', '该', '各', '每', '此', '这些', '那些', '自己',
        '其', '其他', '另', '另外', '只', '仅', '几', '多', '大', '小', '高', '低', '上',
        '下', '前', '后', '左', '右', '内', '外', '间', '的话', '吧', '呢', '啊', '哦', '呀',
        '嗯', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '零', '年', '月', '日'
    }

    try:
        # 尝试加载停用词文件
        if os.path.exists(stopwords_path):
            with open(stopwords_path, 'r', encoding='utf-8') as f:
                stopwords = set(f.read().splitlines())
            print(f"成功加载停用词表，共 {len(stopwords)} 个停用词")
            return stopwords
        else:
            print(f"停用词文件不存在: {stopwords_path}，使用基本停用词")
            return basic_stopwords
    except Exception as e:
        print(f"加载停用词表时出错: {e}，使用基本停用词")
        return basic_stopwords


def load_finance_dict(finance_dict_path):
    """
    加载金融领域词典，如果文件不存在则使用内置的金融词汇

    参数:
        finance_dict_path (str): 金融领域词典文件路径

    返回:
        bool: 是否成功加载
    """
    # 内置的金融领域常用词汇，如果文件加载失败将使用这个
    finance_terms = [
        "金融科技", "金融IT", "证券", "银行", "保险", "基金", "信托", "期货", "期权",
        "股票", "债券", "理财", "投资", "风险", "收益", "资产", "负债", "权益",
        "流动性", "杠杆", "融资", "融券", "做市", "交易", "结算", "清算", "托管",
        "估值", "风控", "合规", "监管", "审计", "会计", "财务", "税务", "法律",
        "咨询", "研究", "分析", "策略", "模型", "算法", "数据", "信息", "系统",
        "平台", "架构", "开发", "测试", "运维", "安全", "云计算", "大数据", "人工智能",
        "区块链", "物联网", "移动互联", "互联网金融", "普惠金融", "供应链金融", "消费金融",
        "财富管理", "资产管理", "投资银行", "商业银行", "投资顾问", "证券公司", "基金公司",
        "保险公司", "信托公司", "期货公司", "私募基金", "公募基金", "对冲基金", "创投基金",
        "股权投资", "并购重组", "IPO", "定增", "可转债", "ABS", "ETF", "LOF", "QDII",
        "沪深300", "中证500", "上证50", "创业板", "科创板", "新三板", "北交所",
        "金融行业", "非银行金融", "金融服务", "金融产品", "金融机构", "金融市场", "金融监管",
        "金融风险", "金融创新", "金融改革", "金融开放", "金融稳定", "金融安全", "金融危机",
        "金融周期", "金融政策", "金融监管", "金融科技", "金融科技公司", "金融科技服务",
        "金融科技产品", "金融科技解决方案", "金融科技平台", "金融科技系统", "金融科技应用",
        "金融科技创新", "金融科技风险", "金融科技监管", "金融科技安全", "金融科技发展",
        "金融科技趋势", "金融科技战略", "金融科技投资", "金融科技合作", "金融科技生态"
    ]

    try:
        # 尝试加载金融词典文件
        if os.path.exists(finance_dict_path):
            jieba.load_userdict(finance_dict_path)
            print(f"成功加载金融词典: {finance_dict_path}")
        else:
            print(f"金融词典文件不存在: {finance_dict_path}，使用内置金融词汇")
            # 添加内置的金融词汇
            for term in finance_terms:
                jieba.add_word(term, freq=20000)
            print(f"成功添加内置金融词汇，共 {len(finance_terms)} 个词")

        # 标记为已加载
        if not hasattr(load_finance_dict, 'loaded'):
            load_finance_dict.loaded = True

        return True
    except Exception as e:
        print(f"加载金融词典时出错: {e}，使用内置金融词汇")
        # 添加内置的金融词汇
        for term in finance_terms:
            try:
                jieba.add_word(term, freq=20000)
            except:
                pass
        print(f"成功添加内置金融词汇，共 {len(finance_terms)} 个词")
        return True


def preprocess_text(text, stopwords_path, finance_dict_path, min_word_len=2, top_words=None):
    """
    对文本进行预处理，包括停用词处理、正则化分词和金融领域词典支持
    优化处理金融研报文本，提高分词质量和关键词提取效果

    参数:
        text (str): 待处理的文本
        stopwords_path (str): 停用词文件路径
        finance_dict_path (str): 金融领域词典文件路径
        min_word_len (int): 最小词长度
        top_words (int): 返回频率最高的前N个词，None表示返回所有词

    返回:
        tuple: (filtered_words, filtered_text) 过滤后的词列表和文本
    """
    start_time = time.time()

    try:
        print("开始文本预处理...")

        # 检查文本是否为空
        if not text or len(text.strip()) == 0:
            print("错误: 输入文本为空")
            return [], ""

        # 加载金融领域词典
        load_finance_dict(finance_dict_path)

        # 加载停用词表
        stopwords = load_stopwords(stopwords_path)

        # 文本清洗：去除特殊字符和数字
        print("正在清洗文本...")
        cleaned_text = clean_text(text)
        print(f"清洗后文本长度: {len(cleaned_text)} 字符")

        # 添加一些金融领域的特殊处理
        # 保留一些重要的金融数字表达式
        cleaned_text = preserve_financial_expressions(cleaned_text)

        # 分词前处理表格文本，确保表格内容被正确分词
        cleaned_text = preprocess_table_text(cleaned_text)

        # 分词
        print("正在进行分词...")
        words = jieba.lcut(cleaned_text)
        print(f"分词结果: {len(words)} 个词")

        # 去除停用词和短词
        print("正在过滤停用词和短词...")
        filtered_words = []
        for word in words:
            word = word.strip()
            # 跳过停用词
            if word in stopwords:
                continue

            # 跳过过短的词
            if len(word) < min_word_len:
                continue

            # 跳过纯数字（但保留包含%等特殊金融符号的数字表达式）
            if word.isdigit():
                continue

            # 跳过特殊字符
            if not any('\u4e00' <= char <= '\u9fff' or char.isalnum() for char in word):
                continue

            filtered_words.append(word)

        print(f"过滤后词数: {len(filtered_words)}")

        # 如果指定了top_words，返回频率最高的前N个词
        if top_words is not None and top_words > 0:
            word_counter = Counter(filtered_words)
            top_words_with_counts = word_counter.most_common(top_words)
            filtered_words = [word for word, _ in top_words_with_counts]
            print(f"选取频率最高的 {len(filtered_words)} 个词")

        # 将过滤后的词列表转换为文本
        filtered_text = " ".join(filtered_words)

        end_time = time.time()
        print(f"文本预处理完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"处理前文本长度: {len(text)} 字符，处理后词数: {len(filtered_words)}")

        return filtered_words, filtered_text

    except Exception as e:
        print(f"文本预处理时出错: {e}")
        return [], ""


def preserve_financial_expressions(text):
    """
    保留金融文本中的重要数字表达式，如百分比、金额等

    参数:
        text (str): 待处理的文本

    返回:
        str: 处理后的文本
    """
    # 保留百分比表达式
    text = re.sub(r'(\d+\.?\d*)\s*%', r'百分之\1', text)

    # 保留金额表达式
    text = re.sub(r'(\d+\.?\d*)\s*(亿|万|千|百|元|美元|欧元|日元|英镑|港元)', r'\1\2', text)

    # 保留增长率、降幅等表达式
    text = re.sub(r'(增长|增加|提高|降低|下降|下跌|上涨|回落)\s*(\d+\.?\d*)\s*%', r'\1百分之\2', text)

    # 保留日期表达式
    text = re.sub(r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', r'\1年\2月\3日', text)

    return text


def preprocess_table_text(text):
    """
    预处理表格文本，确保表格内容被正确分词

    参数:
        text (str): 待处理的文本

    返回:
        str: 处理后的文本
    """
    # 识别表格标记
    table_pattern = r'表格\d+（第\d+页）:\n'
    table_sections = re.split(table_pattern, text)

    if len(table_sections) <= 1:
        return text

    # 处理表格内容
    processed_text = table_sections[0]  # 第一部分是非表格内容

    for i in range(1, len(table_sections)):
        section = table_sections[i]
        # 分割表格行
        lines = section.split('\n')

        if len(lines) <= 2:  # 表格至少需要标题行和一行数据
            processed_text += section
            continue

        # 处理表格行，在单元格之间添加特殊标记，帮助分词
        processed_lines = []
        for line in lines:
            # 替换表格分隔符
            processed_line = line.replace(' | ', '，')
            processed_lines.append(processed_line)

        # 重新组合表格文本
        processed_text += '\n'.join(processed_lines)

    return processed_text


def clean_text(text):
    """
    清洗文本，去除特殊字符、数字等

    参数:
        text (str): 待清洗的文本

    返回:
        str: 清洗后的文本
    """
    # 去除URL
    text = re.sub(r'https?://\S+|www\.\S+', '', text)

    # 去除HTML标签
    text = re.sub(r'<.*?>', '', text)

    # 去除邮箱
    text = re.sub(r'\S*@\S*\s?', '', text)

    # 去除特殊字符和数字，但保留中文、英文和基本标点
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9.,，。、；：''""（）()？?!！]+', ' ', text)

    # 去除多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()

    return text


def extract_sentences(text):
    """
    将文本分割成句子

    参数:
        text (str): 待分割的文本

    返回:
        list: 句子列表
    """
    # 使用标点符号分割文本为句子
    sentences = re.split(r'[。！？!?；;]+', text)

    # 过滤空句子并去除首尾空白
    sentences = [s.strip() for s in sentences if s.strip()]

    return sentences
