#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
研报情感分析完整版 - 演示运行
模拟notebook的核心功能运行
"""

import os
import sys
import warnings
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import jieba
import jieba.analyse
from datetime import datetime

# 忽略警告
warnings.filterwarnings('ignore')

# 设置matplotlib字体
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

print("🚀 研报情感分析完整版 - 演示运行")
print("=" * 60)

# 1. 设置文件路径
PDF_PATH = "data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf"
POSITIVE_DICT_PATH = "data/CFSD中文金融情感词典/正面词典.csv"
NEGATIVE_DICT_PATH = "data/CFSD中文金融情感词典/负面词典.csv"
STOPWORDS_PATH = "data/stopwords.txt"

print(f"\n📁 使用的文件路径:")
print(f"  📄 PDF文件: {PDF_PATH}")
print(f"  📚 正面词典: {POSITIVE_DICT_PATH}")
print(f"  📚 负面词典: {NEGATIVE_DICT_PATH}")
print(f"  🚫 停用词: {STOPWORDS_PATH}")

# 2. PDF文本提取
print(f"\n📄 步骤1: PDF文本提取")
try:
    import pdfplumber
    
    with pdfplumber.open(PDF_PATH) as pdf:
        text = ""
        tables = []
        
        for page_num, page in enumerate(pdf.pages):
            # 提取文本
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
            
            # 提取表格
            page_tables = page.extract_tables()
            for table in page_tables:
                if table and len(table) > 1:
                    try:
                        df = pd.DataFrame(table[1:], columns=table[0])
                        tables.append(df)
                    except:
                        pass
    
    print(f"✅ PDF文本提取成功")
    print(f"  • 文本长度: {len(text):,} 字符")
    print(f"  • 表格数量: {len(tables)} 个")
    print(f"  • 文本样本: {text[:200]}...")
    
except Exception as e:
    print(f"❌ PDF提取失败: {e}")
    text = "这是一个测试文本，用于演示情感分析功能。金融科技发展迅速，带来了很多机遇和挑战。"
    tables = []

# 3. 文本预处理
print(f"\n🔄 步骤2: 文本预处理")
try:
    # 简单清洗
    import re
    cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9.,，。、；：''""（）()？?!！\\s]+', ' ', text)
    cleaned_text = re.sub(r'\\s+', ' ', cleaned_text).strip()
    
    # 分词
    words = jieba.lcut(cleaned_text)
    
    # 加载停用词
    stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
    
    if os.path.exists(STOPWORDS_PATH):
        try:
            with open(STOPWORDS_PATH, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:
                        stopwords.add(word)
        except:
            pass
    
    # 过滤词语
    filtered_words = [w for w in words if len(w) >= 2 and w not in stopwords and not w.isdigit()]
    filtered_text = ' '.join(filtered_words)
    
    print(f"✅ 文本预处理完成")
    print(f"  • 原始词数: {len(words):,}")
    print(f"  • 过滤后词数: {len(filtered_words):,}")
    print(f"  • 停用词数量: {len(stopwords):,}")
    
except Exception as e:
    print(f"❌ 文本预处理失败: {e}")
    filtered_words = []
    filtered_text = text

# 4. 关键词提取
print(f"\n🔍 步骤3: 关键词提取")
try:
    # 使用jieba的TextRank
    keywords = jieba.analyse.textrank(filtered_text, topK=20, withWeight=True)
    
    print(f"✅ 关键词提取完成，共 {len(keywords)} 个关键词")
    print(f"  前10个关键词:")
    for i, (word, weight) in enumerate(keywords[:10], 1):
        print(f"    {i:2d}. {word:<10} (权重: {weight:.4f})")
    
except Exception as e:
    print(f"❌ 关键词提取失败: {e}")
    keywords = [("金融", 0.8), ("科技", 0.7), ("发展", 0.6)]

# 5. 情感分析
print(f"\n📊 步骤4: 情感分析")

# 5.1 加载情感词典
sentiment_dict = {}

# 默认情感词
default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定']
default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定']

for word in default_positive:
    sentiment_dict[word] = 1.0
for word in default_negative:
    sentiment_dict[word] = -1.0

# 加载正面词典
if os.path.exists(POSITIVE_DICT_PATH):
    try:
        df = pd.read_csv(POSITIVE_DICT_PATH)
        if not df.empty:
            words = df.iloc[:, 0].dropna().astype(str)
            for word in words:
                if word.strip():
                    sentiment_dict[word.strip()] = 1.0
        print(f"✅ 加载正面词典: {len([w for w, s in sentiment_dict.items() if s > 0])} 个正面词")
    except Exception as e:
        print(f"⚠️ 加载正面词典失败: {e}")

# 加载负面词典
if os.path.exists(NEGATIVE_DICT_PATH):
    try:
        df = pd.read_csv(NEGATIVE_DICT_PATH)
        if not df.empty:
            words = df.iloc[:, 0].dropna().astype(str)
            for word in words:
                if word.strip():
                    sentiment_dict[word.strip()] = -1.0
        print(f"✅ 加载负面词典: {len([w for w, s in sentiment_dict.items() if s < 0])} 个负面词")
    except Exception as e:
        print(f"⚠️ 加载负面词典失败: {e}")

# 5.2 词典情感分析
print(f"\n📚 5.1 基于词典的情感分析")
try:
    words = jieba.lcut(filtered_text)
    
    total_score = 0
    matched_words = []
    
    for word in words:
        if word in sentiment_dict:
            score = sentiment_dict[word]
            total_score += score
            matched_words.append((word, score))
    
    # 归一化得分
    if len(words) > 0:
        dict_score = total_score / len(words)
    else:
        dict_score = 0
    
    # 计算关键词情感得分
    dict_keywords = []
    for keyword, weight in keywords:
        if keyword in sentiment_dict:
            score = sentiment_dict[keyword]
        else:
            score = 0
            for word in sentiment_dict:
                if word in keyword:
                    score += sentiment_dict[word] * 0.5
        dict_keywords.append((keyword, score, weight))
    
    print(f"✅ 词典情感分析完成")
    print(f"  • 整体情感得分: {dict_score:+.4f}")
    print(f"  • 情感倾向: {'正面' if dict_score > 0.01 else '负面' if dict_score < -0.01 else '中性'}")
    print(f"  • 匹配情感词: {len(matched_words)} 个")
    
    # 显示匹配的情感词
    if matched_words:
        positive_words = [(w, s) for w, s in matched_words if s > 0]
        negative_words = [(w, s) for w, s in matched_words if s < 0]
        
        print(f"  • 正面词 ({len(positive_words)} 个): {', '.join([w for w, s in positive_words[:10]])}")
        print(f"  • 负面词 ({len(negative_words)} 个): {', '.join([w for w, s in negative_words[:10]])}")
    
except Exception as e:
    print(f"❌ 词典情感分析失败: {e}")
    dict_score = 0
    dict_keywords = []
    matched_words = []

# 5.3 简化的FinBERT情感分析（使用规则替代）
print(f"\n🤖 5.2 简化的情感分析（替代FinBERT）")
try:
    # 使用简单规则模拟FinBERT
    positive_indicators = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升', '机遇', '优势']
    negative_indicators = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '威胁', '劣势']
    
    words = jieba.lcut(filtered_text)
    pos_count = sum(1 for word in words if any(pi in word for pi in positive_indicators))
    neg_count = sum(1 for word in words if any(ni in word for ni in negative_indicators))
    
    if len(words) > 0:
        finbert_score = (pos_count - neg_count) / len(words)
    else:
        finbert_score = 0
    
    # 关键词情感得分
    finbert_keywords = []
    for keyword, weight in keywords:
        score = 0
        if any(pi in keyword for pi in positive_indicators):
            score += 0.3
        if any(ni in keyword for ni in negative_indicators):
            score -= 0.3
        finbert_keywords.append((keyword, score, weight))
    
    print(f"✅ 简化情感分析完成")
    print(f"  • 整体情感得分: {finbert_score:+.4f}")
    print(f"  • 情感倾向: {'正面' if finbert_score > 0.01 else '负面' if finbert_score < -0.01 else '中性'}")
    print(f"  • 正面指标词: {pos_count} 个")
    print(f"  • 负面指标词: {neg_count} 个")
    
except Exception as e:
    print(f"❌ 简化情感分析失败: {e}")
    finbert_score = 0
    finbert_keywords = []

# 6. 结果比较和可视化
print(f"\n📊 步骤5: 结果比较和可视化")

# 6.1 比较两种方法
combined_score = (dict_score + finbert_score) / 2
score_difference = abs(dict_score - finbert_score)

print(f"\n🔄 两种方法比较:")
print(f"  • 词典方法得分: {dict_score:+.4f}")
print(f"  • 简化方法得分: {finbert_score:+.4f}")
print(f"  • 综合得分: {combined_score:+.4f}")
print(f"  • 得分差异: {score_difference:.4f}")

# 6.2 创建可视化图表
try:
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 图1: 情感分析方法对比
    methods = ['Dictionary\nMethod', 'Simplified\nMethod']
    scores = [dict_score, finbert_score]
    colors = ['green' if s > 0 else 'red' if s < 0 else 'gray' for s in scores]
    
    bars1 = ax1.bar(methods, scores, color=colors, alpha=0.7, edgecolor='black')
    ax1.set_title('Sentiment Analysis Methods Comparison', fontweight='bold')
    ax1.set_ylabel('Sentiment Score')
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax1.grid(axis='y', alpha=0.3)
    
    for bar, score in zip(bars1, scores):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + (0.01 if height >= 0 else -0.02),
                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 图2: 关键词权重分布
    if keywords:
        top_keywords = keywords[:10]
        words = [item[0] for item in top_keywords]
        weights = [item[1] for item in top_keywords]
        
        ax2.barh(words, weights, color='skyblue', alpha=0.7)
        ax2.set_title('Top Keywords by Weight', fontweight='bold')
        ax2.set_xlabel('Weight')
        ax2.grid(axis='x', alpha=0.3)
    
    # 图3: 情感词分布
    if matched_words:
        positive_words = [w for w, s in matched_words if s > 0]
        negative_words = [w for w, s in matched_words if s < 0]
        
        sentiment_counts = [len(positive_words), len(negative_words)]
        sentiment_labels = ['Positive Words', 'Negative Words']
        sentiment_colors = ['green', 'red']
        
        ax3.pie(sentiment_counts, labels=sentiment_labels, colors=sentiment_colors, 
                autopct='%1.1f%%', startangle=90)
        ax3.set_title('Sentiment Words Distribution', fontweight='bold')
    
    # 图4: 综合评估
    assessment_data = {
        'Dictionary Score': dict_score,
        'Simplified Score': finbert_score,
        'Combined Score': combined_score
    }
    
    ax4.bar(assessment_data.keys(), assessment_data.values(), 
            color=['blue', 'orange', 'purple'], alpha=0.7)
    ax4.set_title('Comprehensive Assessment', fontweight='bold')
    ax4.set_ylabel('Sentiment Score')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax4.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for i, (key, value) in enumerate(assessment_data.items()):
        ax4.text(i, value + (0.01 if value >= 0 else -0.02), f'{value:.3f}', 
                ha='center', va='bottom' if value >= 0 else 'top', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('demo_results.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 可视化图表已生成: demo_results.png")
    
except Exception as e:
    print(f"⚠️ 可视化生成失败: {e}")

# 7. 最终总结
print(f"\n" + "=" * 60)
print(f"📊 研报情感分析结果总结")
print(f"=" * 60)

print(f"\n📄 文档信息:")
print(f"  • 文件: {os.path.basename(PDF_PATH)}")
print(f"  • 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"  • 文本长度: {len(text):,} 字符")
print(f"  • 有效词汇: {len(filtered_words):,} 个")
print(f"  • 关键词数: {len(keywords)} 个")

print(f"\n🔍 关键词 (前10个):")
for i, (word, weight) in enumerate(keywords[:10], 1):
    print(f"  {i:2d}. {word:<10} (权重: {weight:.4f})")

print(f"\n📊 情感分析结果:")
print(f"  • 词典方法: {dict_score:+.4f} ({'正面' if dict_score > 0.01 else '负面' if dict_score < -0.01 else '中性'})")
print(f"  • 简化方法: {finbert_score:+.4f} ({'正面' if finbert_score > 0.01 else '负面' if finbert_score < -0.01 else '中性'})")
print(f"  • 综合评估: {combined_score:+.4f} ({'正面' if combined_score > 0.01 else '负面' if combined_score < -0.01 else '中性'})")

print(f"\n💡 结果解读:")
if combined_score > 0.02:
    print(f"  • 研报整体呈现积极正面的情感倾向")
elif combined_score < -0.02:
    print(f"  • 研报整体呈现消极负面的情感倾向")
else:
    print(f"  • 研报整体情感倾向相对中性")

if score_difference < 0.05:
    print(f"  • 两种分析方法结果较为一致，可信度较高")
else:
    print(f"  • 两种分析方法存在一定差异，建议进一步分析")

print(f"\n🎉 演示运行完成！")
print(f"=" * 60)
