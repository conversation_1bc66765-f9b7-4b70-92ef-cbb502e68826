一款高性能的金融研报文本分析工具，集成了TextRank关键词提取和多种情感分析技术，让您轻松洞察研报背后的情感倾向。支持并行处理和多种分析方法，性能更强，分析更全面！新版本增强了表格文本处理、金融词典匹配和情感分析可视化，提供更精准的分析结果！

## 📖 项目简介（小白必读）

这个项目是一个专门用来分析金融研报的工具，它可以：

1. **自动提取PDF文件中的所有内容**（包括文字和表格）
2. **找出研报中的关键词**（使用TextRank算法）
3. **分析研报的情感倾向**（是积极的还是消极的）
4. **生成直观的图表**展示分析结果

如果你是金融分析师、研究人员或投资者，这个工具可以帮你快速了解一份研报的核心内容和情感倾向，节省阅读时间。

**特别说明**：本项目针对中文金融研报优化，支持完整提取PDF内容（包括表格），并使用两种方法进行情感分析（情感词典和FinBERT深度学习模型）。

## 🌟 功能特点

- **超强PDF文本提取**：
  - 多引擎提取技术，确保PDF内容100%完整提取
  - 专业表格识别，精准提取表格内容不丢失
  - 并行处理多页PDF，提取速度提升3-5倍
  - 智能引擎选择，自动选择最佳提取结果
  - 多重备选方案，即使复杂PDF也能成功提取

- **增强的文本预处理**：
  - 优化的金融领域词典支持，内置常用金融术语
  - 改进的停用词处理，更精准地过滤无意义词汇
  - 特殊处理金融数字表达式和百分比
  - 优化表格文本的预处理，保留表格结构信息

- **多种关键词提取方法**：
  - 增强的TextRank算法，优化处理金融研报文本
  - 改进的关键词与代表性句子匹配算法
  - 混合模式（结合多种算法结果）
  - 自动模式（智能选择最佳方法）

- **双重情感分析**：
  - 增强的金融情感词典，扩充金融IT领域专用词汇
  - 优化的情感词匹配算法，支持复合词和短语匹配
  - 改进的FinBERT模型应用，更好地处理中文金融文本
  - 详细的情感分析对比和多级一致性评估
  - 综合情感得分计算，结合两种方法的优势

- **增强的可视化**：
  - 支持多种图表格式（PNG、JPG、PDF、SVG）
  - 详细的情感分析比较可视化：
    - 情感得分散点图（带趋势线和相关系数）
    - 情感倾向热力图
    - 一致性级别分布饼图
    - 得分差异最大的关键词对比图
    - 情感分布条形图
    - 整体情感分析仪表盘
  - 关键词情感分布图
  - 情感分析一致性饼图
  - 关键词云图
  - 情感词分布统计图

- **高度可定制**：
  - 丰富的命令行参数支持
  - 模块化设计，易于扩展和定制
  - 支持静默模式和调试模式

## 📋 系统要求

- Python 3.8+
- 核心依赖：
  - pandas, pdfplumber, jieba, textrank4zh
  - transformers, torch (用于FinBERT)
  - matplotlib, wordcloud (用于可视化)
  - tqdm (用于进度显示)

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基本使用

将您的研报PDF文件放在`data`目录下，然后运行：

```bash
cd src
python main.py
```

系统会自动处理`data`目录下的第一个PDF文件，并将结果保存在`results`目录中。

### 高级用法

您可以通过丰富的命令行参数自定义分析过程：

```bash
python main.py -f path/to/your/report.pdf -k 50 --keyword-method all --sentiment-method both --parallel --vis-format svg
```

#### 输入输出参数
- `-f, --file`：指定要分析的PDF文件路径
- `-o, --output`：指定结果输出目录

#### 关键词提取参数
- `-k, --keywords`：指定要提取的关键词数量（默认：30）
- `--keyword-method`：关键词提取方法（auto/textrank4zh/jieba/tfidf/all）
- `--min-word-len`：最小词长度（默认：2）

#### 情感分析参数
- `--sentiment-method`：情感分析方法（dict/finbert/both）

#### 可视化参数
- `--no-vis`：不生成可视化图表
- `--vis-format`：可视化图表格式（png/jpg/pdf/svg）

#### 性能参数
- `--parallel`：使用并行处理
- `--max-workers`：最大工作线程数（默认：4）
- `--silent`：静默模式，减少输出信息

#### 调试参数
- `--debug`：启用调试模式，保存更多中间信息

## 📊 输出结果

系统会在结果目录中生成以下文件：

1. **提取的文本**：`extracted_text_[时间戳].txt`
2. **提取的表格**：`extracted_tables_[时间戳].xlsx`
3. **预处理后的文本**：`preprocessed_text_[时间戳].txt`
4. **关键词列表**：`keywords_[时间戳].txt`（包含关键词及其代表性句子）
5. **情感分析结果**：`sentiment_comparison_[时间戳].xlsx`
6. **可视化图表**（保存在`visualizations`子目录）：
   - 基本情感分析对比：`sentiment_comparison_[时间戳].[格式]`
   - 详细情感分析比较（保存在`detailed_comparison`子目录）：
     - 情感得分散点图：`sentiment_scores_scatter.png`
     - 情感倾向热力图：`sentiment_heatmap.png`
     - 一致性级别分布饼图：`sentiment_agreement_levels.png`
     - 得分差异最大的关键词对比图：`top_diff_keywords_sentiment.png`
     - 情感分布条形图：`sentiment_distribution.png`
     - 整体情感分析仪表盘：`overall_sentiment_dashboard.png`
   - 情感词典关键词情感：`dict_keyword_sentiment_[时间戳].[格式]`
   - FinBERT关键词情感：`finbert_keyword_sentiment_[时间戳].[格式]`
   - 情感分析一致性：`sentiment_agreement_[时间戳].[格式]`
   - 关键词云图：`keyword_cloud_[时间戳].[格式]`
   - 情感词分布：`sentiment_distribution_[时间戳].[格式]`

## � 项目文件结构（小白指南）

项目由以下主要文件和目录组成，每个文件都有特定的功能：

```
研报情感分析/
├── data/                   # 存放研报PDF文件和情感词典
│   ├── 会计金融情绪词典/    # 情感分析使用的词典
│   │   ├── 正面词典.txt    # 积极情感词汇
│   │   └── 负面词典.txt    # 消极情感词汇
│   └── *.pdf              # 您要分析的研报PDF文件
├── results/               # 分析结果保存目录
│   └── visualizations/    # 可视化图表保存目录
├── src/                   # 源代码目录
│   ├── finbert/           # FinBERT模型相关文件
│   ├── main.py            # 主程序入口
│   ├── pdf_extractor.py   # PDF提取模块
│   ├── text_preprocessor.py # 文本预处理模块
│   ├── keyword_extractor.py # 关键词提取模块
│   ├── sentiment_analyzer.py # 情感分析模块
│   ├── visualizer.py      # 可视化模块
│   └── fix_keywords.py    # 关键词修复工具
├── example.py             # 使用示例
└── requirements.txt       # 项目依赖列表
```

### 核心文件功能说明（通俗版）

- **main.py**：整个系统的"大脑"，协调所有模块工作，处理命令行参数
- **pdf_extractor.py**：PDF"阅读器"，负责从PDF中提取所有文字和表格
- **text_preprocessor.py**：文本"清洗工"，去除无用词汇，规范化文本格式
- **keyword_extractor.py**：关键词"侦探"，找出文本中最重要的词语
- **sentiment_analyzer.py**：情感"分析师"，判断文本情感是积极还是消极
- **visualizer.py**：图表"设计师"，将分析结果转化为直观的图表

## �🔍 系统架构

系统采用高度模块化的设计，由以下核心组件组成：

1. **PDF提取器**（`pdf_extractor.py`）：
   - 多引擎提取技术（PyMuPDF、pdfplumber、camelot-py、PyPDF2、pdfminer.six）
   - 专业表格识别与提取，支持复杂表格结构
   - 智能引擎选择算法，自动选择最佳提取结果
   - 并行处理多页PDF，大幅提升处理速度
   - 多重备选方案和错误恢复机制，确保提取成功率

2. **文本预处理器**（`text_preprocessor.py`）：
   - 高效的文本清洗和规范化
   - 优化的停用词处理，更精准地过滤无意义词汇
   - 增强的金融领域词典支持，内置常用金融术语
   - 特殊处理金融数字表达式和百分比
   - 优化表格文本的预处理，保留表格结构信息

3. **关键词提取器**（`keyword_extractor.py`）：
   - 增强的TextRank算法，优化处理金融研报文本
   - 改进的关键词与代表性句子匹配算法
   - 支持多种关键词提取算法
   - 自动选择最佳方法

4. **情感分析器**（`sentiment_analyzer.py`）：
   - 增强的金融情感词典，扩充金融IT领域专用词汇
   - 优化的情感词匹配算法，支持复合词和短语匹配
   - 改进的FinBERT模型应用，更好地处理中文金融文本
   - 详细的情感分析对比和多级一致性评估
   - 综合情感得分计算，结合两种方法的优势

5. **可视化器**（`visualizer.py`）：
   - 支持多种图表格式
   - 增强的情感分析比较可视化
   - 详细的情感分析仪表盘
   - 丰富的可视化选项
   - 自适应布局和样式

6. **主程序**（`main.py`）：
   - 处理命令行参数
   - 协调各模块工作
   - 提供详细的进度和结果报告

## 💡 技术原理

### 并行PDF处理

系统使用Python的`concurrent.futures`模块实现并行处理：

1. 将PDF文档分割为多个页面
2. 创建线程池并行处理每个页面
3. 使用进度条显示处理进度
4. 合并处理结果，保持页面顺序

这种方法可以显著提高处理速度，特别是对于大型PDF文档。

### 多种关键词提取方法

系统支持多种关键词提取方法，并可以智能选择或组合使用：

1. **TextRank**：基于图的排序算法，考虑词语间的共现关系
   - textrank4zh实现：更专注于中文文本
   - jieba实现：处理速度更快

2. **TF-IDF**：基于词频-逆文档频率的统计方法
   - 适合提取文档中的特定主题词

3. **混合模式**：结合多种算法的结果
   - 综合考虑不同算法的优势
   - 提高关键词提取的准确性和覆盖面

### 增强的情感分析

系统实现了两种互补的情感分析方法，并进行了多项优化：

1. **增强的基于词典的情感分析**：
   - 扩充的金融领域情感词典，包含金融IT专业术语
   - 多层次匹配算法：直接匹配、部分匹配、窗口匹配和复合词匹配
   - 优化的情感得分计算，考虑词语权重和上下文
   - 快速且无需网络连接，适合批量处理

2. **优化的FinBERT情感分析**：
   - 改进的中文金融文本处理
   - 上下文感知的关键词情感分析
   - 加权平均的整体情感得分计算
   - 能够理解复杂的情感表达和语义关系

3. **综合情感分析**：
   - 多级一致性评估（完全一致、基本一致、部分一致、不一致）
   - 智能加权的综合情感得分，结合两种方法的优势
   - 详细的情感分布统计和可视化
   - 关键词情感差异分析，识别情感表达的复杂性

系统提供了丰富的可视化工具，帮助用户深入理解情感分析结果，发现潜在的情感模式和趋势。

## 📝 使用案例

### 案例1：批量分析研报情感趋势

```bash
# 创建一个脚本来批量处理多个研报
for file in ./data/*.pdf; do
    python src/main.py -f "$file" --silent --output "results/$(basename "$file" .pdf)"
done

# 分析结果趋势
python analyze_trends.py
```

### 案例2：深入分析关键词

```bash
# 提取更多关键词并使用所有方法
python src/main.py -f data/report.pdf -k 100 --keyword-method all --debug
```

### 案例3：生成高质量可视化报告

```bash
# 生成SVG格式的可视化图表
python src/main.py -f data/report.pdf --vis-format svg
```

## 🔧 性能优化技巧

1. **使用并行处理**：对于大型PDF，使用`--parallel`参数可显著提高处理速度
2. **调整工作线程数**：根据CPU核心数调整`--max-workers`参数
3. **选择合适的关键词提取方法**：
   - 对于短文本，使用`--keyword-method jieba`更高效
   - 对于长文本，使用`--keyword-method textrank4zh`更准确
   - 对于重要分析，使用`--keyword-method all`更全面
4. **使用静默模式**：批量处理时使用`--silent`参数减少输出
5. **调整最小词长**：使用`--min-word-len 3`可以过滤掉更多无意义的短词

## 📚 参考资料

- [TextRank算法详解](https://web.eecs.umich.edu/~mihalcea/papers/mihalcea.emnlp04.pdf)
- [FinBERT: 金融领域的BERT模型](https://arxiv.org/abs/1908.10063)
- [金融文本情感分析综述](https://www.mdpi.com/1911-8074/13/5/101)
- [Python并行处理最佳实践](https://docs.python.org/3/library/concurrent.futures.html)
- [金融NLP研究前沿](https://arxiv.org/abs/2106.14204)

## 🛠️ 常见问题解答

**Q: 如何处理非常大的PDF文件？**
A: 使用`--parallel --max-workers 8`参数启用并行处理，并增加工作线程数。

**Q: 为什么FinBERT分析失败？**
A: FinBERT需要下载预训练模型，请确保网络连接正常。如果网络受限，系统会自动使用备选的情感分析方法。

**Q: 如何提高关键词提取的准确性？**
A: 尝试使用`--keyword-method all`组合多种方法，或者扩充金融词汇字典。

**Q: 如何自定义情感词典？**
A: 您可以修改`data/会计金融情绪词典`目录下的正面词典和负面词典文件。

**Q: 系统支持哪些语言？**
A: 系统主要针对中文优化，但基本支持英文文本。对于其他语言，可能需要调整分词和情感词典。

