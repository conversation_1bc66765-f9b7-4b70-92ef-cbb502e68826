"""
修复研报情感分析系统中的两个问题：
1. TextRank4Keyword 的 NetworkX 兼容性问题
2. FinBERT 模型下载问题（使用国内镜像）
"""

import os
import sys
import shutil
from pathlib import Path
import re

def fix_textrank4zh():
    """修复 TextRank4zh 库的 NetworkX 兼容性问题"""
    try:
        import textrank4zh
        import networkx as nx
        
        # 检查 NetworkX 版本
        nx_version = nx.__version__
        print(f"当前 NetworkX 版本: {nx_version}")
        
        if int(nx_version.split('.')[0]) >= 3:
            # 找到 TextRank4zh 库的安装位置
            tr4zh_path = os.path.dirname(textrank4zh.__file__)
            print(f"TextRank4zh 库路径: {tr4zh_path}")
            
            # 备份原始文件
            keyword_file = os.path.join(tr4zh_path, "TextRank4Keyword.py")
            backup_file = os.path.join(tr4zh_path, "TextRank4Keyword.py.bak")
            
            if os.path.exists(keyword_file):
                # 创建备份
                shutil.copy2(keyword_file, backup_file)
                print(f"已创建备份: {backup_file}")
                
                # 读取文件内容
                with open(keyword_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换 from_numpy_matrix 为 from_numpy_array
                if 'from_numpy_matrix' in content:
                    content = content.replace('from_numpy_matrix', 'from_numpy_array')
                    
                    # 写回文件
                    with open(keyword_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("成功修复 TextRank4Keyword.py 文件")
                else:
                    print("文件中未找到 from_numpy_matrix，可能已经修复或使用了不同的方法")
            else:
                print(f"未找到文件: {keyword_file}")
        else:
            print("NetworkX 版本低于 3.0，不需要修复")
    
    except ImportError as e:
        print(f"导入错误: {e}")
    except Exception as e:
        print(f"修复 TextRank4zh 时出错: {e}")

def download_finbert_from_mirror():
    """从国内镜像下载 FinBERT 模型"""
    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        print("开始从国内镜像下载 FinBERT 模型...")
        
        # 设置 Hugging Face 镜像
        os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
        
        # 设置缓存目录
        cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "models_cache")
        finbert_dir = os.path.join(cache_dir, "finbert")
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(finbert_dir, exist_ok=True)
        
        print(f"模型将保存到: {finbert_dir}")
        
        # 下载并缓存模型和分词器
        print("下载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(
            "ProsusAI/finbert", 
            cache_dir=cache_dir,
            mirror='tuna',
            local_files_only=False
        )
        
        print("下载模型...")
        model = AutoModelForSequenceClassification.from_pretrained(
            "ProsusAI/finbert", 
            cache_dir=cache_dir,
            mirror='tuna',
            local_files_only=False
        )
        
        # 保存到本地目录
        print("保存分词器到本地...")
        tokenizer.save_pretrained(finbert_dir)
        
        print("保存模型到本地...")
        model.save_pretrained(finbert_dir)
        
        print(f"FinBERT 模型已成功下载并保存到: {finbert_dir}")
        
        # 测试加载模型
        print("测试加载模型...")
        test_tokenizer = AutoTokenizer.from_pretrained(finbert_dir)
        test_model = AutoModelForSequenceClassification.from_pretrained(finbert_dir)
        
        # 简单测试
        text = "The company reported strong earnings this quarter."
        inputs = test_tokenizer(text, return_tensors="pt")
        with torch.no_grad():
            outputs = test_model(**inputs)
        
        print("模型测试成功！")
        
        # 修改 sentiment_analyzer.py 文件，使其使用本地模型
        update_sentiment_analyzer(finbert_dir)
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装 transformers 和 torch 库:")
        print("pip install transformers torch")
    except Exception as e:
        print(f"下载 FinBERT 模型时出错: {e}")
        print("尝试使用备选方法...")
        download_finbert_manually()

def download_finbert_manually():
    """手动下载 FinBERT 模型（备选方法）"""
    try:
        import requests
        import zipfile
        import io
        
        print("使用备选方法下载 FinBERT 模型...")
        
        # 设置模型文件 URL（可以替换为其他可用的模型文件链接）
        model_url = "https://huggingface.co/ProsusAI/finbert/resolve/main/pytorch_model.bin"
        config_url = "https://huggingface.co/ProsusAI/finbert/resolve/main/config.json"
        vocab_url = "https://huggingface.co/ProsusAI/finbert/resolve/main/vocab.txt"
        
        # 设置缓存目录
        cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "models_cache")
        finbert_dir = os.path.join(cache_dir, "finbert")
        os.makedirs(finbert_dir, exist_ok=True)
        
        # 下载文件
        for name, url in [("模型文件", model_url), ("配置文件", config_url), ("词汇表", vocab_url)]:
            print(f"下载{name}...")
            response = requests.get(url, stream=True)
            if response.status_code == 200:
                file_path = os.path.join(finbert_dir, os.path.basename(url))
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                print(f"{name}下载完成: {file_path}")
            else:
                print(f"下载{name}失败: HTTP {response.status_code}")
        
        print(f"FinBERT 模型文件已保存到: {finbert_dir}")
        
        # 修改 sentiment_analyzer.py 文件，使其使用本地模型
        update_sentiment_analyzer(finbert_dir)
        
    except Exception as e:
        print(f"手动下载 FinBERT 模型时出错: {e}")

def update_sentiment_analyzer(model_path):
    """更新 sentiment_analyzer.py 文件，使其使用本地模型"""
    try:
        # 找到 sentiment_analyzer.py 文件
        src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "src")
        analyzer_file = os.path.join(src_dir, "sentiment_analyzer.py")
        
        if os.path.exists(analyzer_file):
            # 创建备份
            backup_file = os.path.join(src_dir, "sentiment_analyzer.py.bak")
            shutil.copy2(analyzer_file, backup_file)
            print(f"已创建备份: {backup_file}")
            
            # 读取文件内容
            with open(analyzer_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换模型路径
            model_path_escaped = model_path.replace('\\', '\\\\')
            
            # 查找并替换模型加载部分
            pattern = r'tokenizer = AutoTokenizer\.from_pretrained\([\'"]ProsusAI/finbert[\'"](.*?)\)'
            replacement = f'tokenizer = AutoTokenizer.from_pretrained("{model_path_escaped}")'
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            pattern = r'model = AutoModelForSequenceClassification\.from_pretrained\([\'"]ProsusAI/finbert[\'"](.*?)\)'
            replacement = f'model = AutoModelForSequenceClassification.from_pretrained("{model_path_escaped}")'
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            # 写回文件
            with open(analyzer_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"成功更新 sentiment_analyzer.py 文件，使用本地模型路径: {model_path}")
        else:
            print(f"未找到文件: {analyzer_file}")
    
    except Exception as e:
        print(f"更新 sentiment_analyzer.py 文件时出错: {e}")

if __name__ == "__main__":
    print("开始修复问题...")
    
    # 修复 TextRank4zh
    print("\n1. 修复 TextRank4zh 库的 NetworkX 兼容性问题")
    fix_textrank4zh()
    
    # 下载 FinBERT 模型
    print("\n2. 从国内镜像下载 FinBERT 模型")
    download_finbert_from_mirror()
    
    print("\n修复完成！请运行测试以验证修复效果。")
