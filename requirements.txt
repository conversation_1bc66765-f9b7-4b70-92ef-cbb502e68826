# 核心依赖 - 基础数据处理和PDF提取
pandas>=1.3.0          # 数据处理和表格操作
pdfplumber>=0.7.0      # PDF文本和表格提取
PyMuPDF>=1.18.0        # 高性能PDF文本提取 (fitz)
camelot-py>=0.10.1     # 专业表格提取
PyPDF2>=2.0.0          # 备选PDF提取
pdfminer.six>=20200517 # 备选PDF提取
jieba>=0.42.1          # 中文分词
textrank4zh>=0.3.0     # 关键词提取
tqdm>=4.62.0           # 进度条显示
openpyxl>=3.0.0        # Excel文件处理

# 情感分析依赖
snownlp>=0.12.3        # 中文情感分析
transformers>=4.5.0    # FinBERT模型支持
torch>=1.8.0           # PyTorch深度学习框架

# 可视化依赖
matplotlib>=3.4.0      # 基础图表绘制
wordcloud>=1.8.0       # 词云图生成
seaborn>=0.11.0        # 高级统计可视化

# 可选依赖 - 增强功能
pytesseract>=0.3.8     # OCR文本识别 (可选)
Pillow>=8.2.0          # 图像处理 (OCR需要)
numpy>=1.20.0          # 数值计算
scikit-learn>=0.24.0   # 机器学习算法
