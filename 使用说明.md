# 研报情感分析完整版 - 使用说明

## 📋 概述

这是一个完整的研报情感分析系统，整合在单个Jupyter Notebook文件中。系统包含PDF文本提取、文本预处理、关键词提取、情感分析（词典法+FinBERT）和可视化展示等完整功能。

## 🚀 快速开始

### 1. 环境准备

确保您的Python环境中安装了以下库：

```bash
pip install pandas numpy matplotlib seaborn
pip install pdfplumber PyMuPDF jieba textrank4zh
pip install transformers torch wordcloud
pip install scikit-learn openpyxl tqdm
```

### 2. 文件准备

将以下文件放在正确的位置：

```
研报情感分析/
├── 研报情感分析完整版.ipynb  # 主分析文件
├── data/                      # 数据目录
│   ├── *.pdf                 # 待分析的PDF文件
│   ├── CFSD中文金融情感词典/
│   │   ├── 正面词典.csv
│   │   └── 负面词典.csv
│   └── stopwords.txt         # 停用词文件
└── src/                      # 源码目录（可选）
    └── finbert/              # 本地FinBERT模型（可选）
```

### 3. 运行分析

1. 打开 `研报情感分析完整版.ipynb`
2. 按顺序执行所有单元格（Kernel → Restart & Run All）
3. 在"8.1 设置文件路径"部分根据实际情况修改文件路径
4. 等待分析完成，查看结果

## 📊 功能特点

### ✅ 已解决的问题

- **字体显示问题**：使用系统默认字体，避免中文显示问题
- **文件整合**：所有代码和输出都在一个notebook中
- **稳定性**：多重错误处理和备选方案
- **完整性**：包含完整的分析流程和结果解释

### 🔧 核心功能

1. **PDF文本提取**
   - 支持多种PDF提取方法（PyMuPDF、pdfplumber等）
   - 自动选择最佳提取结果
   - 表格数据提取

2. **文本预处理**
   - 智能中文分词
   - 停用词过滤
   - 文本清洗和规范化

3. **关键词提取**
   - TextRank算法
   - TF-IDF算法
   - 多方法融合

4. **情感分析**
   - 基于金融情感词典的传统方法
   - 基于FinBERT的深度学习方法
   - 两种方法的结果比较

5. **可视化展示**
   - 情感分析对比图
   - 关键词情感分布图
   - 方法一致性分析图
   - 关键词云图

## 📈 输出结果

系统会在notebook中直接显示：

1. **文本摘要**：提取的文本长度、关键词数量等基本信息
2. **关键词列表**：按权重排序的重要关键词
3. **情感分析结果**：两种方法的情感得分和倾向
4. **详细分析**：关键词代表性句子、匹配的情感词等
5. **可视化图表**：多种图表直观展示分析结果
6. **数据表格**：结构化的分析结果数据

## ⚠️ 注意事项

1. **首次运行**：可能需要下载FinBERT模型，请确保网络连接
2. **文件路径**：请根据实际情况修改文件路径设置
3. **依赖库**：如果某些库不可用，系统会自动使用备选方案
4. **结果解读**：分析结果仅供参考，建议结合人工判断

## 🔧 故障排除

### 常见问题

1. **PDF文件无法读取**
   - 检查文件路径是否正确
   - 确保PDF文件没有密码保护
   - 尝试将PDF文件放在data目录下

2. **字体显示问题**
   - 系统已配置使用默认字体，应该不会出现此问题
   - 如仍有问题，请检查matplotlib配置

3. **FinBERT模型加载失败**
   - 系统会自动使用备选的情感分析方法
   - 可以手动下载模型到src/finbert目录

4. **可视化图表不显示**
   - 检查matplotlib是否正确安装
   - 确保在Jupyter环境中运行

## 📞 技术支持

如果遇到问题，请检查：

1. Python版本（建议3.7+）
2. 依赖库是否完整安装
3. 文件路径是否正确
4. 数据文件是否存在

## 🎯 使用建议

1. **首次使用**：建议使用提供的示例PDF文件进行测试
2. **批量分析**：可以修改代码循环处理多个PDF文件
3. **结果保存**：可以将分析结果导出为Excel或CSV文件
4. **定制化**：可以根据需要调整关键词数量、情感阈值等参数

---

**🎉 祝您使用愉快！如有任何问题，请随时联系技术支持团队。**
