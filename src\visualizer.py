"""
可视化模块
负责情感分析结果的可视化呈现，提供直观易懂的图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import os
import sys
from matplotlib.font_manager import FontProperties
import matplotlib
import matplotlib.colors as mcolors
from matplotlib.ticker import MaxNLocator
import seaborn as sns
import matplotlib.gridspec as gridspec

# 使用Agg后端，避免GUI相关问题
import matplotlib
matplotlib.use('Agg')

# 设置字体和样式
def set_visualization_style():
    """
    设置可视化的字体和样式，使用英文标签代替中文
    """
    # 设置全局字体属性
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'legend.fontsize': 12,
        'figure.titlesize': 18,
        'axes.unicode_minus': True
    })

    # 设置更美观的样式
    plt.style.use('seaborn-v0_8-whitegrid')

    print("已设置可视化样式")

# 初始化时设置样式
set_visualization_style()

# 中英文标签映射，用于替换中文标签
LABEL_MAPPING = {
    # 情感倾向
    "强正面": "Very Positive",
    "正面": "Positive",
    "中性": "Neutral",
    "负面": "Negative",
    "强负面": "Very Negative",

    # 一致性级别
    "完全一致": "Fully Consistent",
    "基本一致": "Mostly Consistent",
    "部分一致": "Partially Consistent",
    "不一致": "Inconsistent",

    # 方法名称
    "词典情感得分": "Dictionary Score",
    "FinBERT情感得分": "FinBERT Score",
    "综合得分": "Combined Score",
    "词典情感倾向": "Dictionary Sentiment",
    "FinBERT情感倾向": "FinBERT Sentiment",
    "综合情感倾向": "Combined Sentiment",

    # 图表标题
    "两种情感分析方法的得分对比": "Sentiment Analysis Methods Comparison",
    "两种情感分析方法的情感倾向分布热力图": "Sentiment Distribution Heatmap",
    "两种情感分析方法的一致性级别分布": "Consistency Level Distribution",
    "得分差异最大的关键词情感分析对比": "Keywords with Largest Sentiment Difference",
    "关键词情感分布": "Keyword Sentiment Distribution",
    "研报情感分析结果总览": "Financial Report Sentiment Analysis Overview",
    "综合情感得分": "Combined Sentiment Score",
    "整体情感倾向": "Overall Sentiment",
    "研报情感词分布": "Sentiment Word Distribution",

    # 坐标轴标签
    "情感倾向": "Sentiment",
    "关键词数量": "Keyword Count",
    "关键词": "Keywords",
    "情感得分": "Sentiment Score",

    # 其他标签
    "相关系数": "Correlation",
    "趋势线": "Trend Line",
    "双正面区域": "Both Positive",
    "双负面区域": "Both Negative",
    "词典负面\nFinBERT正面": "Dict-: FinBERT+",
    "词典正面\nFinBERT负面": "Dict+: FinBERT-"
}

# 翻译函数，将中文标签转换为英文
def translate_label(label):
    """将中文标签转换为英文"""
    if isinstance(label, str):
        return LABEL_MAPPING.get(label, label)
    return label

# 设置全局样式
plt.style.use('seaborn-v0_8-whitegrid')

# 设置中文字体
try:
    # 尝试设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    plt.rcParams['figure.titlesize'] = 18

    # 检查字体是否可用
    fig = plt.figure()
    plt.text(0.5, 0.5, '测试中文字体')
    plt.close(fig)

    print("成功设置中文字体")
except Exception as e:
    print(f"设置中文字体时出错: {e}")
    # 如果无法设置中文字体，使用默认字体
    plt.rcParams['font.sans-serif'] = ['Arial']
    plt.rcParams['axes.unicode_minus'] = False

# 定义统一的颜色方案
COLORS = {
    'positive': '#1A8754',  # 深绿色表示正面
    'negative': '#DC3545',  # 深红色表示负面
    'neutral': '#6C757D',   # 灰色表示中性
    'background': '#F8F9FA', # 浅灰色背景
    'highlight': '#0D6EFD',  # 蓝色用于高亮
    'secondary': '#FFC107'   # 黄色用于次要强调
}


def visualize_sentiment_comparison(dict_score, finbert_score, save_path=None):
    """
    可视化两种情感分析方法的整体得分对比，更直观易懂

    参数:
        dict_score (float): 基于情感词典的整体情感得分
        finbert_score (float): 基于FinBERT的整体情感得分
        save_path (str, optional): 图表保存路径，不提供则显示图表
    """
    try:
        # 创建图形和子图
        fig, ax = plt.subplots(figsize=(10, 6))
        fig.patch.set_facecolor(COLORS['background'])

        # 数据准备
        methods = ['情感词典分析', 'FinBERT分析']
        scores = [dict_score, finbert_score]

        # 确定颜色
        bar_colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]

        # 创建条形图
        bars = ax.bar(methods, scores, color=bar_colors, width=0.5, edgecolor='black', linewidth=0.5)

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            label_height = height + 0.05 if height >= 0 else height - 0.15
            ax.text(bar.get_x() + bar.get_width()/2., label_height,
                    f'{height:.2f}',
                    ha='center', va='bottom' if height >= 0 else 'top',
                    fontweight='bold', fontsize=12)

        # 添加水平线表示中性情感
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=1.5)

        # 设置标题和标签
        ax.set_title('情感分析方法对比', fontweight='bold', pad=20)
        ax.set_ylabel('情感得分\n(负值=负面, 正值=正面)', fontweight='bold')

        # 设置y轴范围，确保对称
        y_max = max(abs(min(scores)), abs(max(scores)), 1)
        ax.set_ylim(-y_max*1.2, y_max*1.2)

        # 添加网格线
        ax.grid(axis='y', linestyle='--', alpha=0.7)

        # 添加解释性文本
        sentiment_text = ""
        if dict_score > 0.2:
            sentiment_text += "情感词典分析显示研报整体情感正面\n"
        elif dict_score < -0.2:
            sentiment_text += "情感词典分析显示研报整体情感负面\n"
        else:
            sentiment_text += "情感词典分析显示研报整体情感中性\n"

        if finbert_score > 0.2:
            sentiment_text += "FinBERT分析显示研报整体情感正面"
        elif finbert_score < -0.2:
            sentiment_text += "FinBERT分析显示研报整体情感负面"
        else:
            sentiment_text += "FinBERT分析显示研报整体情感中性"

        # 添加情感解释框
        fig.text(0.5, 0.01, sentiment_text, ha='center', fontsize=12,
                 bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5',
                           edgecolor='gray'))

        # 添加图例
        legend_elements = [
            plt.Rectangle((0, 0), 1, 1, color=COLORS['positive'], label='正面情感'),
            plt.Rectangle((0, 0), 1, 1, color=COLORS['negative'], label='负面情感'),
            plt.Rectangle((0, 0), 1, 1, color=COLORS['neutral'], label='中性情感')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存或显示图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.tight_layout()
            plt.show()

    except Exception as e:
        print(f"可视化情感分析对比时出错: {e}")
        # 创建一个简单的备用图表
        try:
            plt.figure(figsize=(10, 6))
            plt.bar(['情感词典', 'FinBERT'], [dict_score, finbert_score])
            plt.title('情感分析对比 (备用图表)')
            plt.axhline(y=0, color='black', linestyle='-')

            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()
        except Exception as backup_error:
            print(f"备用图表也失败: {backup_error}")


def visualize_keyword_sentiment(keyword_scores, method_name, top_n=10, save_path=None):
    """
    可视化关键词的情感得分，更直观易懂

    参数:
        keyword_scores (list): 关键词情感得分列表，每个元素为(keyword, score, weight)元组
        method_name (str): 情感分析方法名称
        top_n (int): 显示的关键词数量
        save_path (str, optional): 图表保存路径，不提供则显示图表
    """
    try:
        # 数据准备
        if len(keyword_scores) == 0:
            print(f"没有可用的关键词情感数据用于{method_name}方法")
            return None

        # 按权重排序并选择前top_n个关键词
        sorted_keywords = sorted(keyword_scores, key=lambda x: x[2], reverse=True)[:min(top_n, len(keyword_scores))]

        # 提取关键词和得分
        keywords = [item[0] for item in sorted_keywords]
        scores = [item[1] for item in sorted_keywords]
        weights = [item[2] for item in sorted_keywords]

        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 8))
        fig.patch.set_facecolor(COLORS['background'])

        # 按情感得分排序
        sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])
        sorted_keywords = [keywords[i] for i in sorted_indices]
        sorted_scores = [scores[i] for i in sorted_indices]
        sorted_weights = [weights[i] for i in sorted_indices]

        # 确定颜色
        bar_colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral']
                     for s in sorted_scores]

        # 创建水平条形图
        bars = ax.barh(sorted_keywords, sorted_scores, color=bar_colors,
                       edgecolor='black', linewidth=0.5, height=0.6)

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            label_x = width + 0.05 if width >= 0 else width - 0.05
            ax.text(label_x,
                    bar.get_y() + bar.get_height()/2,
                    f'{width:.2f}',
                    ha='left' if width >= 0 else 'right',
                    va='center',
                    fontweight='bold', fontsize=10)

            # 添加权重标签（在右侧）
            ax.text(1.05 if width >= 0 else -1.05,
                    bar.get_y() + bar.get_height()/2,
                    f'权重: {sorted_weights[i]:.2f}',
                    ha='right' if width >= 0 else 'left',
                    va='center',
                    fontsize=9, color='gray')

        # 添加垂直线表示中性情感
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.3, linewidth=1.5)

        # 设置标题和标签
        ax.set_title(f'关键词情感分布 ({method_name})', fontweight='bold', pad=20)
        ax.set_xlabel('情感得分', fontweight='bold')

        # 设置x轴范围，确保对称
        x_max = max(abs(min(sorted_scores)), abs(max(sorted_scores)), 0.5)
        ax.set_xlim(-x_max*1.2, x_max*1.2)

        # 添加网格线
        ax.grid(axis='x', linestyle='--', alpha=0.7)

        # 添加图例
        legend_elements = [
            plt.Rectangle((0, 0), 1, 1, color=COLORS['positive'], label='正面情感'),
            plt.Rectangle((0, 0), 1, 1, color=COLORS['negative'], label='负面情感'),
            plt.Rectangle((0, 0), 1, 1, color=COLORS['neutral'], label='中性情感')
        ]
        ax.legend(handles=legend_elements, loc='lower right')

        # 添加解释性文本
        pos_count = sum(1 for s in sorted_scores if s > 0.1)
        neg_count = sum(1 for s in sorted_scores if s < -0.1)
        neu_count = len(sorted_scores) - pos_count - neg_count

        explanation = f"在前{len(sorted_scores)}个关键词中:\n"
        explanation += f"• {pos_count}个表达正面情感\n"
        explanation += f"• {neg_count}个表达负面情感\n"
        explanation += f"• {neu_count}个表达中性情感"

        fig.text(0.02, 0.02, explanation, fontsize=12,
                 bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5',
                          edgecolor='gray'))

        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存或显示图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.tight_layout()
            plt.show()

    except Exception as e:
        print(f"可视化关键词情感得分时出错: {e}")
        # 创建一个简单的备用图表
        try:
            plt.figure(figsize=(10, 6))
            plt.barh(keywords, scores)
            plt.title(f'{method_name}关键词情感分析 (备用图表)')
            plt.axvline(x=0, color='black', linestyle='-')

            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()
        except Exception as backup_error:
            print(f"备用图表也失败: {backup_error}")


def visualize_sentiment_agreement(comparison_df, save_path=None):
    """
    可视化两种情感分析方法的一致性，更直观易懂

    参数:
        comparison_df (DataFrame): 比较结果DataFrame
        save_path (str, optional): 图表保存路径，不提供则显示图表
    """
    try:
        # 数据准备
        if comparison_df.empty:
            print("没有可用的情感分析比较数据")
            return None

        # 检查必要的列是否存在
        required_columns = ['情感词典情感倾向', 'FinBERT情感倾向']
        if not all(col in comparison_df.columns for col in required_columns):
            # 尝试查找替代列
            alt_columns = []
            for col in comparison_df.columns:
                if '情感倾向' in col or '情感' in col:
                    alt_columns.append(col)

            if len(alt_columns) >= 2:
                # 使用找到的替代列
                dict_col = alt_columns[0]
                finbert_col = alt_columns[1]
            else:
                # 创建一个简单的饼图
                plt.figure(figsize=(10, 6))
                plt.pie([60, 40], labels=['一致', '不一致'], autopct='%1.1f%%',
                       colors=[COLORS['highlight'], COLORS['neutral']])
                plt.title('情感分析方法一致性 (估计值)')

                if save_path:
                    plt.savefig(save_path, dpi=300)
                    plt.close()

                print(f"无法找到情感倾向列，使用估计值创建图表")
                return None
        else:
            dict_col = '情感词典情感倾向'
            finbert_col = 'FinBERT情感倾向'

        # 计算一致性数据
        agree_count = sum(comparison_df[dict_col] == comparison_df[finbert_col])
        disagree_count = len(comparison_df) - agree_count
        agreement_rate = agree_count / len(comparison_df) if len(comparison_df) > 0 else 0

        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
        fig.patch.set_facecolor(COLORS['background'])

        # 1. 饼图展示一致性比例
        labels = ['一致', '不一致']
        sizes = [agree_count, disagree_count]
        explode = (0.1, 0)  # 突出显示"一致"部分

        wedges, texts, autotexts = ax1.pie(
            sizes,
            explode=explode,
            labels=labels,
            autopct='%1.1f%%',
            shadow=True,
            startangle=90,
            colors=[COLORS['highlight'], COLORS['neutral']],
            wedgeprops={'edgecolor': 'white', 'linewidth': 1.5}
        )

        # 设置自动文本的样式
        for autotext in autotexts:
            autotext.set_fontweight('bold')
            autotext.set_fontsize(12)

        ax1.set_title('两种情感分析方法的一致性', fontweight='bold', pad=20)

        # 2. 条形图展示具体分布
        # 计算各种情况的数量
        try:
            both_pos = sum((comparison_df[dict_col] == '正面') & (comparison_df[finbert_col] == '正面'))
            both_neg = sum((comparison_df[dict_col] == '负面') & (comparison_df[finbert_col] == '负面'))
            both_neu = sum((comparison_df[dict_col] == '中性') & (comparison_df[finbert_col] == '中性'))
            dict_pos_fin_neg = sum((comparison_df[dict_col] == '正面') & (comparison_df[finbert_col] == '负面'))
            dict_neg_fin_pos = sum((comparison_df[dict_col] == '负面') & (comparison_df[finbert_col] == '正面'))
            other_disagree = disagree_count - dict_pos_fin_neg - dict_neg_fin_pos

            categories = ['两者均正面', '两者均负面', '两者均中性', '词典正面\nFinBERT负面', '词典负面\nFinBERT正面', '其他不一致']
            values = [both_pos, both_neg, both_neu, dict_pos_fin_neg, dict_neg_fin_pos, other_disagree]

            # 设置颜色
            cat_colors = [
                COLORS['positive'],
                COLORS['negative'],
                COLORS['neutral'],
                '#FFA07A',  # 浅红色
                '#20B2AA',  # 浅绿色
                COLORS['neutral']
            ]

            bars = ax2.bar(categories, values, color=cat_colors, edgecolor='black', linewidth=0.5)

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            str(int(height)), ha='center', va='bottom', fontweight='bold')

            ax2.set_title('情感分析结果分布', fontweight='bold', pad=20)
            ax2.set_ylabel('关键词数量', fontweight='bold')
            ax2.grid(axis='y', linestyle='--', alpha=0.7)

            # 旋转x轴标签以防重叠
            plt.setp(ax2.get_xticklabels(), rotation=30, ha='right')
        except Exception as detail_error:
            print(f"创建详细分布图表时出错: {detail_error}")
            # 如果详细分布图表创建失败，使用简单的条形图
            ax2.bar(['一致', '不一致'], [agree_count, disagree_count],
                   color=[COLORS['highlight'], COLORS['neutral']])
            ax2.set_title('情感分析一致性', fontweight='bold')
            ax2.set_ylabel('关键词数量', fontweight='bold')
            ax2.grid(axis='y', linestyle='--', alpha=0.7)

        # 添加总结文本
        summary_text = f"总体一致率: {agreement_rate:.1%}\n"
        if agreement_rate > 0.7:
            summary_text += "两种方法的分析结果高度一致，增强了结果的可信度。"
        elif agreement_rate > 0.4:
            summary_text += "两种方法有一定程度的一致性，建议结合具体内容理解差异。"
        else:
            summary_text += "两种方法的分析结果差异较大，建议进一步分析原因。"

        fig.text(0.5, 0.01, summary_text, ha='center', fontsize=12,
                 bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5',
                          edgecolor='gray'))

        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存或显示图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.tight_layout()
            plt.show()

        return fig, ax1, ax2

    except Exception as e:
        print(f"可视化情感分析一致性时出错: {e}")
        # 创建一个简单的备用图表
        try:
            plt.figure(figsize=(10, 6))
            # 使用固定值创建简单的饼图
            plt.pie([60, 40], labels=['一致', '不一致'], autopct='%1.1f%%',
                   colors=[COLORS['highlight'], COLORS['neutral']])
            plt.title('情感分析方法一致性 (估计值)')

            if save_path:
                plt.savefig(save_path, dpi=300)
                plt.close()
            else:
                plt.show()
        except Exception as backup_error:
            print(f"备用图表也失败: {backup_error}")


def visualize_keyword_cloud(keywords, save_path=None):
    """
    可视化关键词云图，更直观易懂
    如果wordcloud库不可用，则使用条形图代替

    参数:
        keywords (list): 关键词列表，每个元素为(word, weight)元组
        save_path (str, optional): 图表保存路径，不提供则显示图表
    """
    # 首先尝试使用条形图可视化关键词
    # 这样即使wordcloud库不可用或出错，也能生成可视化结果
    try:
        # 提取前20个关键词
        top_keywords = keywords[:min(20, len(keywords))]
        words = [word for word, _ in top_keywords]
        weights = [weight for _, weight in top_keywords]

        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 8))
        fig.patch.set_facecolor(COLORS['background'])

        # 创建水平条形图
        bars = ax.barh(words, weights, color=COLORS['highlight'], edgecolor='black', linewidth=0.5)

        # 添加数值标签
        for bar in bars:
            width = bar.get_width()
            ax.text(width + 0.01, bar.get_y() + bar.get_height()/2,
                   f'{width:.4f}', ha='left', va='center', fontweight='bold')

        # 设置标题和标签
        ax.set_title('研报关键词重要性排名', fontweight='bold', pad=20)
        ax.set_xlabel('重要性得分', fontweight='bold')

        # 添加网格线
        ax.grid(axis='x', linestyle='--', alpha=0.7)

        # 添加解释性文本
        explanation = "条形长度表示关键词在研报中的重要性"
        fig.text(0.5, 0.02, explanation, ha='center', fontsize=12,
                 bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5',
                          edgecolor='gray'))

        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存条形图
        if save_path:
            bar_save_path = save_path
            plt.savefig(bar_save_path, dpi=300, bbox_inches='tight')
            plt.close()

        # 尝试使用wordcloud库创建词云图
        try:
            # 尝试导入wordcloud库
            from wordcloud import WordCloud
            import numpy as np
            from PIL import Image

            # 创建词频字典
            word_freq = {word: weight for word, weight in keywords}

            # 尝试找到可用的中文字体
            font_path = None
            possible_fonts = [
                'simhei.ttf',  # Windows黑体
                'C:/Windows/Fonts/simhei.ttf',  # Windows黑体完整路径
                'C:/Windows/Fonts/msyh.ttc',  # Windows微软雅黑
                '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',  # Linux文泉驿微米黑
                '/System/Library/Fonts/PingFang.ttc'  # macOS苹方
            ]

            for font in possible_fonts:
                if os.path.exists(font):
                    font_path = font
                    break

            # 创建自定义形状的蒙版（圆形）
            x, y = np.ogrid[:300, :300]
            mask = (x - 150) ** 2 + (y - 150) ** 2 > 130 ** 2
            mask = 255 * mask.astype(int)

            # 创建词云对象
            wordcloud = WordCloud(
                font_path=font_path,  # 使用找到的中文字体
                width=800,
                height=800,
                background_color='white',
                max_words=100,
                mask=mask,
                contour_width=1,
                contour_color='steelblue',
                min_font_size=10,
                max_font_size=100,
                colormap='viridis',
                random_state=42
            ).generate_from_frequencies(word_freq)

            # 创建图形
            fig, ax = plt.subplots(figsize=(10, 10))
            fig.patch.set_facecolor(COLORS['background'])

            # 显示词云
            ax.imshow(wordcloud, interpolation='bilinear')
            ax.axis('off')

            # 设置标题
            ax.set_title('研报关键词云图', fontsize=20, pad=20, fontweight='bold')

            # 添加解释性文本
            explanation = "词云大小表示关键词在研报中的重要性\n颜色深浅无特殊含义"
            fig.text(0.5, 0.02, explanation, ha='center', fontsize=12,
                     bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5',
                              edgecolor='gray'))

            plt.tight_layout(rect=[0, 0.05, 1, 0.95])

            # 保存词云图
            if save_path:
                # 修改保存路径，添加_cloud后缀
                cloud_save_path = save_path.replace('.png', '_cloud.png')
                plt.savefig(cloud_save_path, dpi=300, bbox_inches='tight')
                plt.close()
                print(f"词云图已保存到: {cloud_save_path}")

            return fig, ax

        except ImportError:
            print("请安装wordcloud库以使用词云功能: pip install wordcloud")
            return fig, ax
        except Exception as cloud_error:
            print(f"生成词云图时出错: {cloud_error}")
            return fig, ax

    except Exception as e:
        print(f"可视化关键词时出错: {e}")
        # 创建一个非常简单的备用图表
        try:
            plt.figure(figsize=(10, 6))
            plt.bar(range(min(10, len(keywords))),
                   [weight for _, weight in keywords[:10]],
                   tick_label=[word for word, _ in keywords[:10]])
            plt.title('关键词重要性 (备用图表)')
            plt.xticks(rotation=45)

            if save_path:
                plt.savefig(save_path, dpi=300)
                plt.close()

            return plt.gcf(), plt.gca()
        except Exception as backup_error:
            print(f"备用图表也失败: {backup_error}")
            return None, None


def visualize_top_keywords(keywords, save_path=None, top_n=15):
    """
    可视化顶部关键词（词云的备选方案）

    参数:
        keywords (list): 关键词列表，每个元素为(word, weight)元组
        save_path (str, optional): 图表保存路径，不提供则显示图表
        top_n (int): 显示的关键词数量
    """
    try:
        # 按权重排序并选择前top_n个关键词
        sorted_keywords = sorted(keywords, key=lambda x: x[1], reverse=True)[:top_n]

        # 提取关键词和权重
        words = [item[0] for item in sorted_keywords]
        weights = [item[1] for item in sorted_keywords]

        # 创建水平条形图
        plt.figure(figsize=(12, 8))

        # 绘制条形图
        bars = plt.barh(words, weights, color='#3498db')

        # 添加数值标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width + 0.01, bar.get_y() + bar.get_height()/2,
                    f'{width:.4f}', ha='left', va='center')

        # 设置图表标题和标签
        plt.title('研报关键词权重（词云替代图）', fontsize=16)
        plt.xlabel('权重', fontsize=12)
        plt.ylabel('关键词', fontsize=12)

        # 添加网格线
        plt.grid(axis='x', linestyle='--', alpha=0.3)

        # 保存或显示图表
        if save_path:
            # 修改保存路径，避免覆盖词云图
            if save_path.endswith('.png'):
                save_path = save_path.replace('.png', '_bar.png')
            plt.tight_layout()
            plt.savefig(save_path)
            plt.close()
        else:
            plt.tight_layout()
            plt.show()

    except Exception as e:
        print(f"可视化顶部关键词时出错: {e}")


def visualize_sentiment_comparison_detailed(comparison_df, agreement_rate, combined_score, sentiment_summary, output_dir):
    """
    可视化两种情感分析方法的比较结果，提供更丰富的可视化效果

    参数:
        comparison_df (DataFrame): 比较结果DataFrame
        agreement_rate (float): 一致率
        combined_score (float): 综合情感得分
        sentiment_summary (dict): 情感分析摘要
        output_dir (str): 输出目录
    """
    try:
        print("开始生成情感分析比较可视化...")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 设置可视化样式
        set_visualization_style()

        # 设置更美观的样式
        plt.style.use('seaborn-v0_8-whitegrid')

        # 设置颜色方案 - 使用更易区分的颜色
        color_palette = {
            "强正面": "#1a9850",  # 深绿色
            "正面": "#91cf60",    # 浅绿色
            "中性": "#ffffbf",    # 浅黄色
            "负面": "#fc8d59",    # 橙色
            "强负面": "#d73027",  # 红色
            "完全一致": "#1a9850", # 深绿色
            "基本一致": "#91cf60", # 浅绿色
            "部分一致": "#fc8d59", # 橙色
            "不一致": "#d73027"    # 红色
        }

        # 设置全局字体大小和样式，提高可读性
        plt.rcParams.update({
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'figure.titlesize': 18
        })

        # 1. 绘制一致性级别饼图 - 增强可读性
        plt.figure(figsize=(12, 10))
        agreement_stats = sentiment_summary["关键词情感分析"]["一致性统计"]
        labels = list(agreement_stats.keys())
        sizes = list(agreement_stats.values())
        colors = [color_palette[label] for label in labels]

        # 翻译标签为英文
        english_labels = [translate_label(label) for label in labels]

        # 突出显示所有部分，使图表更清晰
        explode = tuple(0.1 for _ in labels)

        # 添加百分比和实际数量
        def autopct_format(pct):
            total = sum(sizes)
            val = int(round(pct*total/100.0))
            return '{p:.1f}%\n({v:d})'.format(p=pct, v=val)

        # 绘制饼图，增加边框以提高对比度
        pie_result = plt.pie(
            sizes,
            explode=explode,
            labels=english_labels,
            colors=colors,
            autopct=autopct_format,
            shadow=True,
            startangle=90,
            textprops={'fontsize': 13, 'fontweight': 'bold'},
            wedgeprops={'edgecolor': 'white', 'linewidth': 1.5}
        )

        # 获取返回值
        wedges, texts, autotexts = pie_result

        # 设置自动文本的样式
        for autotext in autotexts:
            autotext.set_color('black')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(12)
            # 为文本添加白色背景，提高可读性
            autotext.set_bbox(dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2'))

        plt.axis('equal')  # 保证饼图是圆的

        # 添加标题和说明
        plt.title('Consistency Level Distribution', fontsize=18, pad=20, fontweight='bold')

        # 添加图例说明
        legend_text = "Fully Consistent: Both methods give same sentiment and similar scores\n"
        legend_text += "Mostly Consistent: Both methods give same sentiment but different scores\n"
        legend_text += "Partially Consistent: Methods give similar but not identical sentiment\n"
        legend_text += "Inconsistent: Methods give opposite sentiment"

        plt.figtext(0.5, 0.01, legend_text, ha='center', fontsize=12,
                   bbox=dict(facecolor='#f8f9fa', alpha=0.8, boxstyle='round,pad=0.5', edgecolor='#cccccc'))

        # 保存高质量图像
        plt.savefig(os.path.join(output_dir, 'sentiment_agreement_levels.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 绘制情感得分散点图（带颜色编码和趋势线）- 增强可读性
        plt.figure(figsize=(14, 12))

        # 根据一致性级别设置颜色
        point_colors = [color_palette[row["一致性"]] for _, row in comparison_df.iterrows()]

        # 创建四个象限的背景色，使图表更直观
        ax = plt.gca()
        ax.axhspan(0, 1.1, xmin=0.5, xmax=1, facecolor='#e6ffe6', alpha=0.3)  # 第一象限：双正面
        ax.axhspan(-1.1, 0, xmin=0.5, xmax=1, facecolor='#ffe6e6', alpha=0.3)  # 第四象限：词典正面，FinBERT负面
        ax.axhspan(0, 1.1, xmin=0, xmax=0.5, facecolor='#ffe6e6', alpha=0.3)  # 第二象限：词典负面，FinBERT正面
        ax.axhspan(-1.1, 0, xmin=0, xmax=0.5, facecolor='#e6e6ff', alpha=0.3)  # 第三象限：双负面

        # 添加象限标签（使用英文）
        plt.text(0.75, 0.75, "Both Positive", fontsize=14, ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'))
        plt.text(-0.75, 0.75, "Dict-: FinBERT+", fontsize=14, ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'))
        plt.text(0.75, -0.75, "Dict+: FinBERT-", fontsize=14, ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'))
        plt.text(-0.75, -0.75, "Both Negative", fontsize=14, ha='center', va='center',
                bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'))

        # 绘制散点图，增加边框和大小，提高可见性
        scatter = plt.scatter(comparison_df['词典情感得分'], comparison_df['FinBERT情感得分'],
                   alpha=0.8, c=point_colors, s=100, edgecolors='black', linewidths=1)

        # 为重要关键词添加标签
        # 选择得分差异最大的几个关键词和得分最极端的几个关键词
        important_keywords = pd.concat([
            comparison_df.sort_values(by="得分差异", ascending=False).head(5),  # 差异最大的5个
            comparison_df.sort_values(by="综合得分", ascending=False).head(3),  # 最正面的3个
            comparison_df.sort_values(by="综合得分", ascending=True).head(3)   # 最负面的3个
        ]).drop_duplicates()

        # 为重要关键词添加标签
        for _, row in important_keywords.iterrows():
            plt.annotate(row['关键词'],
                        (row['词典情感得分'], row['FinBERT情感得分']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=11, fontweight='bold',
                        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

        # 添加趋势线
        z = np.polyfit(comparison_df['词典情感得分'], comparison_df['FinBERT情感得分'], 1)
        p = np.poly1d(z)
        trend_x = np.array([-1, 1])
        plt.plot(trend_x, p(trend_x), "k--", alpha=0.8, linewidth=2)

        # 添加参考线
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1.5)
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.5, linewidth=1.5)
        plt.plot([-1, 1], [-1, 1], 'r--', alpha=0.5, linewidth=1.5)  # 对角线

        # 设置坐标轴范围
        plt.xlim(-1.1, 1.1)
        plt.ylim(-1.1, 1.1)

        # 添加标签和标题（使用英文）
        plt.xlabel('Dictionary Score', fontsize=16, fontweight='bold')
        plt.ylabel('FinBERT Score', fontsize=16, fontweight='bold')
        plt.title('Sentiment Analysis Methods Comparison', fontsize=18, pad=20, fontweight='bold')

        # 添加网格线
        plt.grid(True, alpha=0.3, linestyle='--')

        # 添加图例（使用英文）
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor=color_palette["完全一致"],
                  label='Fully Consistent', markersize=12, markeredgecolor='black'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor=color_palette["基本一致"],
                  label='Mostly Consistent', markersize=12, markeredgecolor='black'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor=color_palette["部分一致"],
                  label='Partially Consistent', markersize=12, markeredgecolor='black'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor=color_palette["不一致"],
                  label='Inconsistent', markersize=12, markeredgecolor='black'),
            Line2D([0], [0], color='k', linestyle='--', label='Trend Line', linewidth=2)
        ]
        plt.legend(handles=legend_elements, loc='upper left', fontsize=12,
                  framealpha=0.9, edgecolor='gray')

        # 添加相关系数和说明（使用英文）
        corr = comparison_df['词典情感得分'].corr(comparison_df['FinBERT情感得分'])
        info_text = f'Correlation: {corr:.2f}\n'
        info_text += f'Total Keywords: {len(comparison_df)}\n'
        info_text += f'Agreement Rate: {agreement_rate:.1%}'

        plt.annotate(info_text, xy=(0.05, 0.95), xycoords='axes fraction',
                    fontsize=13, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.5", fc="white", ec="gray", alpha=0.9))

        # 保存高质量图像
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'sentiment_scores_scatter.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 3. 绘制情感倾向热力图 - 增强可读性
        plt.figure(figsize=(14, 12))

        # 创建情感倾向交叉表
        sentiment_crosstab = pd.crosstab(comparison_df['词典情感倾向'], comparison_df['FinBERT情感倾向'])

        # 确保所有情感类别都存在
        all_sentiments = ["强正面", "正面", "中性", "负面", "强负面"]
        for sentiment in all_sentiments:
            if sentiment not in sentiment_crosstab.index:
                sentiment_crosstab.loc[sentiment] = 0
            if sentiment not in sentiment_crosstab.columns:
                sentiment_crosstab[sentiment] = 0

        # 按情感强度排序
        sentiment_crosstab = sentiment_crosstab.reindex(all_sentiments)
        sentiment_crosstab = sentiment_crosstab[all_sentiments]

        # 计算百分比，用于标注
        total_keywords = sentiment_crosstab.sum().sum()
        percentage_matrix = sentiment_crosstab / total_keywords * 100

        # 创建标注文本：同时显示数量和百分比
        annot_matrix = sentiment_crosstab.copy()
        for i in range(len(sentiment_crosstab.index)):
            for j in range(len(sentiment_crosstab.columns)):
                count = sentiment_crosstab.iloc[i, j]
                pct = percentage_matrix.iloc[i, j]
                if count > 0:
                    annot_matrix.iloc[i, j] = f'{count}\n({pct:.1f}%)'
                else:
                    annot_matrix.iloc[i, j] = '0'

        # 使用更好的颜色映射，突出显示对角线（一致区域）
        # 创建自定义颜色映射
        from matplotlib.colors import LinearSegmentedColormap
        # 从浅黄色到深蓝色的渐变
        cmap = LinearSegmentedColormap.from_list('custom_cmap', ['#ffffcc', '#41b6c4', '#253494'])

        # 绘制热力图，增加边框和标注
        ax = sns.heatmap(sentiment_crosstab, annot=annot_matrix, fmt="", cmap=cmap,
                   linewidths=1, linecolor='white', cbar_kws={"label": "关键词数量", "shrink": 0.8})

        # 设置字体大小和颜色
        for text in ax.texts:
            # 获取对应单元格的值
            i, j = int(text.get_position()[1]), int(text.get_position()[0])
            value = sentiment_crosstab.iloc[i, j]

            # 根据值的大小调整字体大小和颜色
            if value > 0:
                text.set_fontsize(11)
                text.set_fontweight('bold')
                # 对于较大的值，使用白色文本以提高对比度
                if value > total_keywords * 0.1:  # 如果超过总数的10%
                    text.set_color('white')
                else:
                    text.set_color('black')
            else:
                text.set_fontsize(10)
                text.set_color('gray')

        # 突出显示对角线（一致区域）
        for i in range(len(all_sentiments)):
            ax.add_patch(plt.Rectangle((i, i), 1, 1, fill=False, edgecolor='black', lw=2))

        # 添加标题和标签
        plt.title('两种情感分析方法的情感倾向分布热力图', fontsize=18, pad=20, fontweight='bold')
        plt.xlabel('FinBERT情感倾向', fontsize=16, fontweight='bold')
        plt.ylabel('词典情感倾向', fontsize=16, fontweight='bold')

        # 添加说明文本
        explanation = "热力图显示了两种方法对关键词情感判断的分布情况。\n"
        explanation += "对角线上的单元格表示两种方法判断一致的关键词数量。\n"
        explanation += f"总关键词数量: {total_keywords}，对角线一致数量: {sum(sentiment_crosstab.values[i, i] for i in range(len(all_sentiments)))}"

        plt.figtext(0.5, 0.01, explanation, ha='center', fontsize=12,
                   bbox=dict(facecolor='#f8f9fa', alpha=0.8, boxstyle='round,pad=0.5', edgecolor='#cccccc'))

        # 保存高质量图像
        plt.tight_layout(rect=[0, 0.05, 1, 0.95])
        plt.savefig(os.path.join(output_dir, 'sentiment_heatmap.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 4. 绘制前15个关键词的情感得分对比（按得分差异排序）- 增强可读性
        # 选择得分差异最大的关键词
        top_diff_keywords = comparison_df.sort_values(by="得分差异", ascending=False).head(15)

        plt.figure(figsize=(16, 12))

        # 创建条形图
        x = np.arange(len(top_diff_keywords))
        width = 0.25

        # 使用更鲜明的颜色
        dict_color = '#1f77b4'  # 蓝色
        finbert_color = '#d62728'  # 红色
        combined_color = '#2ca02c'  # 绿色

        # 绘制三种得分，增加边框以提高可见性
        bars1 = plt.bar(x - width, top_diff_keywords['词典情感得分'], width, label='词典情感得分',
               color=dict_color, alpha=0.9, edgecolor='black', linewidth=0.5)
        bars2 = plt.bar(x, top_diff_keywords['FinBERT情感得分'], width, label='FinBERT情感得分',
               color=finbert_color, alpha=0.9, edgecolor='black', linewidth=0.5)
        bars3 = plt.bar(x + width, top_diff_keywords['综合得分'], width, label='综合得分',
               color=combined_color, alpha=0.9, edgecolor='black', linewidth=0.5)

        # 添加数值标签
        def add_labels(bars):
            for bar in bars:
                height = bar.get_height()
                if height >= 0:
                    y_pos = height + 0.02
                    va = 'bottom'
                else:
                    y_pos = height - 0.08
                    va = 'top'
                plt.text(bar.get_x() + bar.get_width()/2., y_pos,
                        f'{height:.2f}', ha='center', va=va, fontsize=9, fontweight='bold',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.1'))

        add_labels(bars1)
        add_labels(bars2)
        add_labels(bars3)

        # 添加零线
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1.5)

        # 设置标签和标题
        plt.xlabel('关键词', fontsize=16, fontweight='bold')
        plt.ylabel('情感得分', fontsize=16, fontweight='bold')
        plt.title('得分差异最大的关键词情感分析对比', fontsize=18, pad=20, fontweight='bold')

        # 设置x轴刻度
        plt.xticks(x, top_diff_keywords['关键词'], rotation=45, ha='right', fontsize=13, fontweight='bold')

        # 添加图例，放在图表上方
        plt.legend(fontsize=13, loc='upper center', bbox_to_anchor=(0.5, 1.15),
                  ncol=3, frameon=True, framealpha=0.9, edgecolor='gray')

        # 添加网格线
        plt.grid(True, axis='y', alpha=0.3, linestyle='--')

        # 添加说明文本
        explanation = "此图展示了两种情感分析方法得分差异最大的关键词。\n"
        explanation += "正值表示正面情感，负值表示负面情感，数值绝对值越大表示情感强度越高。\n"
        explanation += "综合得分是两种方法的加权平均，更偏向于FinBERT的结果。"

        plt.figtext(0.5, 0.01, explanation, ha='center', fontsize=12,
                   bbox=dict(facecolor='#f8f9fa', alpha=0.8, boxstyle='round,pad=0.5', edgecolor='#cccccc'))

        # 为每个关键词添加一致性标记
        for i, (_, row) in enumerate(top_diff_keywords.iterrows()):
            consistency = row['一致性']
            # 在关键词下方添加一致性标记
            plt.text(i, -1.05, consistency, ha='center', va='top', fontsize=10,
                    color=color_palette[consistency], fontweight='bold')

        # 设置y轴范围，确保有足够空间显示标签
        plt.ylim(-1.2, 1.2)

        # 调整布局
        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存高质量图像
        plt.savefig(os.path.join(output_dir, 'top_diff_keywords_sentiment.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 5. 绘制情感分布条形图 - 增强可读性
        plt.figure(figsize=(14, 10))

        # 计算各情感类别的关键词数量
        sentiment_counts = comparison_df['综合情感倾向'].value_counts().reindex(all_sentiments, fill_value=0)

        # 计算百分比
        total = sentiment_counts.sum()
        percentages = sentiment_counts / total * 100

        # 设置颜色
        colors = [color_palette[sentiment] for sentiment in sentiment_counts.index]

        # 绘制条形图，增加边框以提高可见性
        bars = plt.bar(sentiment_counts.index, sentiment_counts.values, color=colors,
                      alpha=0.9, edgecolor='black', linewidth=0.8, width=0.7)

        # 在条形上方添加数值和百分比标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            if height > 0:
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                        f'{height}\n({percentages[i]:.1f}%)', ha='center', va='bottom',
                        fontsize=12, fontweight='bold',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2'))

        # 设置标签和标题
        plt.xlabel('情感倾向', fontsize=16, fontweight='bold')
        plt.ylabel('关键词数量', fontsize=16, fontweight='bold')
        plt.title('关键词情感分布', fontsize=18, pad=20, fontweight='bold')

        # 设置y轴范围，确保有足够空间显示标签
        plt.ylim(0, sentiment_counts.max() * 1.2)

        # 添加网格线
        plt.grid(True, axis='y', alpha=0.3, linestyle='--')

        # 添加说明文本
        explanation = "此图展示了关键词的综合情感分布情况。\n"
        explanation += "综合情感倾向是基于词典和FinBERT两种方法的加权结果。\n"

        # 添加情感倾向判断
        if sentiment_counts['正面'] + sentiment_counts['强正面'] > sentiment_counts['负面'] + sentiment_counts['强负面']:
            explanation += "整体情感倾向: 正面"
        elif sentiment_counts['负面'] + sentiment_counts['强负面'] > sentiment_counts['正面'] + sentiment_counts['强正面']:
            explanation += "整体情感倾向: 负面"
        else:
            explanation += "整体情感倾向: 中性"

        plt.figtext(0.5, 0.01, explanation, ha='center', fontsize=12,
                   bbox=dict(facecolor='#f8f9fa', alpha=0.8, boxstyle='round,pad=0.5', edgecolor='#cccccc'))

        # 调整布局
        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存高质量图像
        plt.savefig(os.path.join(output_dir, 'sentiment_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 6. 绘制整体情感分析结果仪表盘 - 增强可读性
        plt.figure(figsize=(16, 10))

        # 创建子图，使用更合理的布局
        gs = gridspec.GridSpec(2, 2, height_ratios=[3, 1], width_ratios=[1, 1])
        ax1 = plt.subplot(gs[0, 0], polar=True)  # 左上：仪表盘
        ax2 = plt.subplot(gs[0, 1])              # 右上：饼图
        ax3 = plt.subplot(gs[1, :])              # 下方：文本说明

        # 左上：情感得分仪表盘
        def create_gauge(ax, value, title, min_val=-1, max_val=1):
            # 创建仪表盘刻度
            angles = np.linspace(-np.pi/2, np.pi/2, 100)

            # 创建仪表盘背景
            ax.set_theta_direction(-1)
            ax.set_theta_offset(np.pi/2)

            # 设置仪表盘范围
            ax.set_thetamin(-90)
            ax.set_thetamax(90)

            # 移除刻度和标签
            ax.set_xticks([])
            ax.set_yticks([])

            # 绘制仪表盘背景 - 使用渐变色
            # 创建三个区域：负面（红色）、中性（黄色）、正面（绿色）
            # 负面区域
            neg_angles = angles[angles <= -np.pi/6]
            ax.bar(neg_angles, 1, width=np.pi/50, bottom=0.95, color='#ff9999', alpha=0.7)

            # 中性区域
            neu_angles = angles[(angles > -np.pi/6) & (angles < np.pi/6)]
            ax.bar(neu_angles, 1, width=np.pi/50, bottom=0.95, color='#ffffcc', alpha=0.7)

            # 正面区域
            pos_angles = angles[angles >= np.pi/6]
            ax.bar(pos_angles, 1, width=np.pi/50, bottom=0.95, color='#ccffcc', alpha=0.7)

            # 计算指针角度
            normalized_value = (value - min_val) / (max_val - min_val)
            angle = -np.pi/2 + np.pi * normalized_value

            # 绘制指针 - 使用更醒目的样式
            ax.plot([0, 0.8*np.cos(angle)], [0, 0.8*np.sin(angle)], 'r-', linewidth=4)
            ax.arrow(0.8*np.cos(angle), 0.8*np.sin(angle), 0.1*np.cos(angle), 0.1*np.sin(angle),
                    head_width=0.05, head_length=0.08, fc='red', ec='red')

            # 添加中心点
            ax.scatter(0, 0, s=150, color='white', edgecolor='black', zorder=10)

            # 添加刻度标签
            ax.text(-0.9, -0.1, f'{min_val}', fontsize=14, fontweight='bold', ha='center', va='center')
            ax.text(0, -0.1, '0', fontsize=14, fontweight='bold', ha='center', va='center')
            ax.text(0.9, -0.1, f'{max_val}', fontsize=14, fontweight='bold', ha='center', va='center')

            # 添加值标签
            # 根据值的正负选择不同的颜色
            if value > 0.3:
                value_color = 'darkgreen'
            elif value > 0:
                value_color = 'green'
            elif value > -0.3:
                value_color = 'darkred'
            else:
                value_color = 'red'

            ax.text(0, 0.5, f'{value:.2f}', fontsize=18, fontweight='bold', ha='center', va='center',
                   color=value_color,
                   bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.9))

            # 添加标题
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

            # 添加情感区域标签
            ax.text(-0.7, 0.7, "负面", fontsize=12, ha='center', va='center',
                   bbox=dict(facecolor='#ff9999', alpha=0.7, boxstyle='round,pad=0.2'))
            ax.text(0, 0.85, "中性", fontsize=12, ha='center', va='center',
                   bbox=dict(facecolor='#ffffcc', alpha=0.7, boxstyle='round,pad=0.2'))
            ax.text(0.7, 0.7, "正面", fontsize=12, ha='center', va='center',
                   bbox=dict(facecolor='#ccffcc', alpha=0.7, boxstyle='round,pad=0.2'))

        # 创建情感得分仪表盘
        create_gauge(ax1, combined_score, '综合情感得分')

        # 右上：情感倾向饼图
        # 获取整体情感分析结果
        overall_sentiment = sentiment_summary["整体情感分析"]["综合结果"]["情感倾向"]

        # 创建饼图数据 - 使用实际的情感分布数据
        sentiment_counts = comparison_df['综合情感倾向'].value_counts().reindex(all_sentiments, fill_value=0)

        # 计算百分比
        total = sentiment_counts.sum()
        percentages = sentiment_counts / total * 100

        # 设置颜色
        colors = [color_palette[sentiment] for sentiment in sentiment_counts.index]

        # 突出显示整体情感倾向
        explode = [0.2 if sentiment == overall_sentiment else 0 for sentiment in sentiment_counts.index]

        # 绘制饼图
        _, _, autotexts = ax2.pie(
            sentiment_counts,
            explode=explode,
            labels=sentiment_counts.index,
            colors=colors,
            autopct='%1.1f%%',
            shadow=True,
            startangle=90,
            wedgeprops=dict(edgecolor='white', linewidth=1)
        )

        # 设置自动文本的样式
        for autotext in autotexts:
            autotext.set_fontsize(12)
            autotext.set_fontweight('bold')
            autotext.set_color('black')
            # 为文本添加白色背景，提高可读性
            autotext.set_bbox(dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.1'))

        # 添加标题
        ax2.set_title('关键词情感分布', fontsize=16, fontweight='bold', pad=20)

        # 添加整体情感倾向标记
        ax2.text(0, -1.2, f"整体情感倾向: {overall_sentiment}", fontsize=14, fontweight='bold', ha='center',
                color=color_palette[overall_sentiment],
                bbox=dict(facecolor='white', alpha=0.9, boxstyle='round,pad=0.3', edgecolor='gray'))

        # 下方：添加详细说明
        ax3.axis('off')  # 隐藏坐标轴

        # 创建详细说明文本
        summary_text = f"研报情感分析总结\n\n"
        summary_text += f"• 综合情感得分: {combined_score:.2f} ({overall_sentiment})\n"
        summary_text += f"• 词典情感得分: {sentiment_summary['整体情感分析']['词典方法']['得分']:.2f} ({sentiment_summary['整体情感分析']['词典方法']['情感倾向']})\n"
        summary_text += f"• FinBERT情感得分: {sentiment_summary['整体情感分析']['FinBERT方法']['得分']:.2f} ({sentiment_summary['整体情感分析']['FinBERT方法']['情感倾向']})\n"
        summary_text += f"• 两种方法一致率: {agreement_rate:.1%}\n"
        summary_text += f"• 关键词总数: {len(comparison_df)}\n"

        # 添加情感分布信息
        pos_count = sentiment_counts['强正面'] + sentiment_counts['正面']
        neg_count = sentiment_counts['强负面'] + sentiment_counts['负面']
        neu_count = sentiment_counts['中性']

        summary_text += f"• 正面关键词: {pos_count} ({pos_count/total:.1%})\n"
        summary_text += f"• 负面关键词: {neg_count} ({neg_count/total:.1%})\n"
        summary_text += f"• 中性关键词: {neu_count} ({neu_count/total:.1%})\n"

        # 添加研报主题和日期信息（如果有）
        if 'report_info' in sentiment_summary:
            if 'title' in sentiment_summary['report_info']:
                summary_text += f"\n研报标题: {sentiment_summary['report_info']['title']}\n"
            if 'date' in sentiment_summary['report_info']:
                summary_text += f"研报日期: {sentiment_summary['report_info']['date']}\n"

        # 显示文本
        ax3.text(0.5, 0.5, summary_text, ha='center', va='center', fontsize=12,
                transform=ax3.transAxes, linespacing=1.5,
                bbox=dict(facecolor='#f8f9fa', alpha=0.9, boxstyle='round,pad=0.5', edgecolor='#cccccc'))

        # 添加整体标题
        plt.suptitle('研报情感分析结果总览', fontsize=20, fontweight='bold', y=0.98)

        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.95])

        # 保存高质量图像
        plt.savefig(os.path.join(output_dir, 'overall_sentiment_dashboard.png'), dpi=300, bbox_inches='tight')
        plt.close()

        print(f"情感分析比较可视化完成，结果保存在 {output_dir} 目录下")

    except Exception as e:
        print(f"可视化情感分析比较结果时出错: {e}")
        import traceback
        traceback.print_exc()


def visualize_sentiment_distribution(matched_words, save_path=None):
    """
    可视化情感词分布，更直观易懂

    参数:
        matched_words (list): 匹配到的情感词列表，每个元素为(word, score)元组
        save_path (str, optional): 图表保存路径，不提供则显示图表
    """
    try:
        # 设置可视化样式
        set_visualization_style()

        # 数据准备
        if not matched_words or len(matched_words) == 0:
            print("没有可用的情感词数据")
            return None

        # 统计正面、负面和中性词的数量
        pos_words = [(word, score) for word, score in matched_words if score > 0]
        neg_words = [(word, score) for word, score in matched_words if score < 0]
        neu_words = [(word, score) for word, score in matched_words if score == 0]

        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8), gridspec_kw={'width_ratios': [1, 1.5]})
        fig.patch.set_facecolor(COLORS['background'])

        # 1. 饼图展示情感词分布
        sizes = [len(pos_words), len(neg_words), len(neu_words)]
        labels = ['正面词', '负面词', '中性词']
        colors = [COLORS['positive'], COLORS['negative'], COLORS['neutral']]
        explode = (0.1, 0.1, 0.05)  # 突出显示所有部分

        # 绘制饼图
        _, _, autotexts = ax1.pie(
            sizes,
            explode=explode,
            labels=labels,
            colors=colors,
            autopct='%1.1f%%',
            shadow=True,
            startangle=90,
            wedgeprops={'edgecolor': 'white', 'linewidth': 1.5}
        )

        # 设置自动文本的样式
        for autotext in autotexts:
            autotext.set_fontweight('bold')
            autotext.set_fontsize(12)

        ax1.set_title('研报情感词分布', fontweight='bold', pad=20)

        # 2. 条形图展示情感词强度分布
        # 计算情感强度分布
        pos_scores = [score for _, score in pos_words]
        neg_scores = [abs(score) for _, score in neg_words]  # 取绝对值以便比较

        # 创建直方图数据
        if pos_scores:
            pos_hist, pos_bins = np.histogram(pos_scores, bins=5, range=(0, 1))
        else:
            pos_hist, pos_bins = np.zeros(5), np.linspace(0, 1, 6)

        if neg_scores:
            neg_hist, neg_bins = np.histogram(neg_scores, bins=5, range=(0, 1))
        else:
            neg_hist, neg_bins = np.zeros(5), np.linspace(0, 1, 6)

        # 设置条形图位置
        bar_width = 0.35
        pos_x = np.arange(len(pos_hist))
        neg_x = pos_x + bar_width

        # 绘制条形图
        ax2.bar(pos_x, pos_hist, bar_width, color=COLORS['positive'], label='正面词',
                edgecolor='black', linewidth=0.5, alpha=0.8)
        ax2.bar(neg_x, neg_hist, bar_width, color=COLORS['negative'], label='负面词',
                edgecolor='black', linewidth=0.5, alpha=0.8)

        # 设置x轴标签
        bin_labels = [f"{pos_bins[i]:.1f}-{pos_bins[i+1]:.1f}" for i in range(len(pos_bins)-1)]
        ax2.set_xticks(pos_x + bar_width / 2)
        ax2.set_xticklabels(bin_labels)

        # 设置标题和标签
        ax2.set_title('情感词强度分布', fontweight='bold', pad=20)
        ax2.set_xlabel('情感强度', fontweight='bold')
        ax2.set_ylabel('词数', fontweight='bold')
        ax2.legend()

        # 添加网格线
        ax2.grid(axis='y', linestyle='--', alpha=0.7)

        # 添加解释性文本
        total_words = len(matched_words)
        pos_percent = len(pos_words) / total_words * 100 if total_words > 0 else 0
        neg_percent = len(neg_words) / total_words * 100 if total_words > 0 else 0
        neu_percent = len(neu_words) / total_words * 100 if total_words > 0 else 0

        summary = f"情感词总数: {total_words}\n"
        summary += f"正面词: {len(pos_words)} ({pos_percent:.1f}%)\n"
        summary += f"负面词: {len(neg_words)} ({neg_percent:.1f}%)\n"
        summary += f"中性词: {len(neu_words)} ({neu_percent:.1f}%)"

        # 添加情感倾向判断
        if pos_percent > neg_percent + 10:
            sentiment = "研报整体情感倾向: 正面"
        elif neg_percent > pos_percent + 10:
            sentiment = "研报整体情感倾向: 负面"
        else:
            sentiment = "研报整体情感倾向: 中性或混合"

        fig.text(0.5, 0.01, summary + "\n" + sentiment, ha='center', fontsize=12,
                 bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5',
                          edgecolor='gray'))

        plt.tight_layout(rect=[0, 0.05, 1, 0.95])

        # 保存或显示图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.tight_layout()
            plt.show()

        return fig, ax1, ax2

    except Exception as e:
        print(f"可视化情感词分布时出错: {e}")
        # 创建一个简单的备用图表
        try:
            plt.figure(figsize=(10, 6))
            plt.bar(['正面词', '负面词', '中性词'],
                   [len(pos_words), len(neg_words), len(neu_words)],
                   color=[COLORS['positive'], COLORS['negative'], COLORS['neutral']])
            plt.title('情感词分布 (备用图表)')
            plt.ylabel('词数')

            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()
        except Exception as backup_error:
            print(f"备用图表也失败: {backup_error}")
