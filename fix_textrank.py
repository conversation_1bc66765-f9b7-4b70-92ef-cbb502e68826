"""
修复 TextRank4Keyword 的 NetworkX 兼容性问题
"""
import os
import sys
import shutil
import importlib.util

def fix_textrank4zh():
    """修复 TextRank4zh 库的 NetworkX 兼容性问题"""
    try:
        # 查找 textrank4zh 库的路径
        spec = importlib.util.find_spec("textrank4zh")
        if spec is None:
            print("未找到 textrank4zh 库，请确保已安装")
            return False
        
        tr4zh_path = os.path.dirname(spec.origin)
        print(f"TextRank4zh 库路径: {tr4zh_path}")
        
        # 查找 TextRank4Keyword.py 文件
        keyword_file = os.path.join(tr4zh_path, "TextRank4Keyword.py")
        if not os.path.exists(keyword_file):
            print(f"未找到文件: {keyword_file}")
            return False
        
        # 创建备份
        backup_file = os.path.join(tr4zh_path, "TextRank4Keyword.py.bak")
        shutil.copy2(keyword_file, backup_file)
        print(f"已创建备份: {backup_file}")
        
        # 读取文件内容
        with open(keyword_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 from_numpy_matrix 为 from_numpy_array
        if 'from_numpy_matrix' in content:
            content = content.replace('from_numpy_matrix', 'from_numpy_array')
            
            # 写回文件
            with open(keyword_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("成功修复 TextRank4Keyword.py 文件")
            return True
        else:
            print("文件中未找到 from_numpy_matrix，可能已经修复或使用了不同的方法")
            return True
    
    except Exception as e:
        print(f"修复 TextRank4zh 时出错: {e}")
        return False

if __name__ == "__main__":
    print("开始修复 TextRank4Keyword 的 NetworkX 兼容性问题...")
    success = fix_textrank4zh()
    if success:
        print("修复成功！")
    else:
        print("修复失败，请手动检查问题。")
