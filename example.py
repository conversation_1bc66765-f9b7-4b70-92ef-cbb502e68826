"""
研报情感分析系统使用示例
演示如何使用研报情感分析系统的主要功能
"""

import os
import sys
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入所需模块
from src.pdf_extractor import extract_text_and_tables_from_pdf
from src.text_preprocessor import preprocess_text
from src.keyword_extractor import extract_keywords_textrank, extract_keywords_with_sentences
from src.sentiment_analyzer import load_sentiment_dict, sentiment_analysis_by_dict, sentiment_analysis_by_finbert, compare_sentiment_results
from src.visualizer import visualize_sentiment_comparison, visualize_keyword_sentiment


def example_basic_usage():
    """
    基本使用示例：分析单个PDF文件
    """
    print("="*50)
    print("基本使用示例：分析单个PDF文件")
    print("="*50)

    # 设置文件路径
    root_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_dir = os.path.join(root_dir, 'data')
    pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]

    if not pdf_files:
        print("错误：未找到PDF文件。请将PDF文件放在data目录下。")
        return

    # 使用第一个PDF文件
    pdf_path = os.path.join(pdf_dir, pdf_files[0])
    stopwords_path = os.path.join(root_dir, 'data', 'stopwords.txt')
    finance_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '金融词汇字典.txt')
    positive_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '正面词典.txt')
    negative_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '负面词典.txt')

    print(f"使用PDF文件: {os.path.basename(pdf_path)}")

    # 1. 从PDF文件中提取文本和表格
    print("\n1. 提取PDF文本和表格...")
    print("   使用优化版本的PDF提取器，确保内容完整提取...")
    text, tables = extract_text_and_tables_from_pdf(pdf_path, max_workers=4)

    # 验证提取结果
    if text is None or not text:
        print("错误：无法从PDF文件中提取文本。")
        return

    # 检查表格提取情况
    if not tables or len(tables) == 0:
        print("注意：未从PDF中提取到表格，或表格提取失败。")

    print(f"成功提取文本，共 {len(text)} 个字符")
    print(f"成功提取表格，共 {len(tables)} 个表格")

    # 2. 文本预处理
    print("\n2. 文本预处理...")
    filtered_words, filtered_text = preprocess_text(text, stopwords_path, finance_dict_path)
    print(f"成功完成文本预处理，处理后共 {len(filtered_words)} 个词语")

    # 3. 关键词提取
    print("\n3. 关键词提取...")
    keywords = extract_keywords_textrank(filtered_text, num_keywords=10, word_min_len=2)
    print("提取的关键词及其权重：")
    for i, (word, weight) in enumerate(keywords):
        print(f"  {i+1}. {word}: {weight:.4f}")

    # 4. 情感分析
    print("\n4. 情感分析...")
    # 加载情感词典
    sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)

    # 基于情感词典的情感分析
    dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(filtered_text, keywords, sentiment_dict)
    print(f"基于情感词典的整体情感得分: {dict_score:.4f}")

    # 基于FinBERT的情感分析
    finbert_score, finbert_keywords = sentiment_analysis_by_finbert(filtered_text, keywords)
    print(f"基于FinBERT的整体情感得分: {finbert_score:.4f}")

    # 比较两种情感分析方法的结果
    comparison_df, agreement_rate = compare_sentiment_results(
        dict_score, finbert_score, dict_keywords, finbert_keywords
    )
    print(f"两种情感分析方法的一致率: {agreement_rate:.2%}")

    # 5. 可视化
    print("\n5. 可视化...")
    # 创建结果目录
    results_dir = os.path.join(root_dir, 'example_results')
    os.makedirs(results_dir, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 可视化两种情感分析方法的整体得分对比
    visualize_sentiment_comparison(
        dict_score, finbert_score,
        save_path=os.path.join(results_dir, f"sentiment_comparison_{timestamp}.png")
    )

    # 可视化基于情感词典的关键词情感得分
    visualize_keyword_sentiment(
        dict_keywords, "情感词典", top_n=10,
        save_path=os.path.join(results_dir, f"dict_keyword_sentiment_{timestamp}.png")
    )

    print(f"\n示例完成！结果已保存到 {results_dir} 目录。")


def example_custom_analysis():
    """
    自定义分析示例：指定参数进行分析
    """
    print("\n" + "="*50)
    print("自定义分析示例：指定参数进行分析")
    print("="*50)

    # 设置文件路径
    root_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_dir = os.path.join(root_dir, 'data')
    pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]

    if not pdf_files:
        print("错误：未找到PDF文件。请将PDF文件放在data目录下。")
        return

    # 使用第一个PDF文件
    pdf_path = os.path.join(pdf_dir, pdf_files[0])
    stopwords_path = os.path.join(root_dir, 'data', 'stopwords.txt')
    finance_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '金融词汇字典.txt')
    positive_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '正面词典.txt')
    negative_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '负面词典.txt')

    print(f"使用PDF文件: {os.path.basename(pdf_path)}")

    # 1. 从PDF文件中提取文本和表格
    print("\n1. 提取PDF文本和表格...")
    print("   使用优化版本的PDF提取器，确保内容完整提取...")
    text, tables = extract_text_and_tables_from_pdf(pdf_path, max_workers=4)

    # 验证提取结果
    if text is None or not text:
        print("错误：无法从PDF文件中提取文本。")
        return

    # 2. 文本预处理
    print("\n2. 文本预处理...")
    filtered_words, filtered_text = preprocess_text(text, stopwords_path, finance_dict_path)

    # 3. 自定义关键词提取 - 提取更多关键词
    print("\n3. 自定义关键词提取 - 提取更多关键词...")
    num_keywords = 20  # 自定义关键词数量
    keywords = extract_keywords_textrank(filtered_text, num_keywords=num_keywords, word_min_len=2)
    print(f"提取了 {len(keywords)} 个关键词")

    # 4. 提取关键词的代表性句子
    print("\n4. 提取关键词的代表性句子...")
    keyword_sentences = extract_keywords_with_sentences(text, keywords[:5], num_sentences=1)
    for keyword, sentences in keyword_sentences.items():
        print(f"\n关键词: {keyword}")
        for i, sentence in enumerate(sentences):
            print(f"  代表性句子: {sentence[:100]}...")

    print("\n示例完成！")


def example_command_line():
    """
    命令行使用示例
    """
    print("\n" + "="*50)
    print("命令行使用示例")
    print("="*50)

    print("您可以使用以下命令行参数运行主程序：")
    print("\npython src/main.py -f path/to/your/report.pdf -k 50 -o custom_output_dir")
    print("\n参数说明：")
    print("  -f, --file：指定要分析的PDF文件路径")
    print("  -k, --keywords：指定要提取的关键词数量（默认：30）")
    print("  -o, --output：指定结果输出目录")
    print("  --no-vis：不生成可视化图表")

    print("\n示例命令：")
    root_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_dir = os.path.join(root_dir, 'data')
    pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]

    if pdf_files:
        pdf_path = os.path.join(pdf_dir, pdf_files[0])
        print(f"python src/main.py -f \"{pdf_path}\" -k 50 -o results_custom")
    else:
        print("python src/main.py -f \"data/your_report.pdf\" -k 50 -o results_custom")


if __name__ == "__main__":
    # 运行示例
    example_basic_usage()
    example_custom_analysis()
    example_command_line()
