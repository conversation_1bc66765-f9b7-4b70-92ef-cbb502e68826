"""
PDF文本和表格提取模块
负责从PDF文件中提取文本内容和表格数据，支持多种PDF格式和表格处理
优化版本：确保PDF内容完整提取，包括文本和表格
"""

import pandas as pd
import pdfplumber
import re
import os
import time
import concurrent.futures
from tqdm import tqdm
import json
import numpy as np

# 尝试导入可选依赖
try:
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("警告: OCR功能不可用，如需使用请安装pytesseract和Pillow: pip install pytesseract pillow")

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    print("警告: PyPDF2不可用，备选PDF提取方法将不可用: pip install PyPDF2")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF不可用，高级PDF提取方法将不可用: pip install PyMuPDF")

try:
    import camelot
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False
    print("警告: camelot-py不可用，高级表格提取方法将不可用: pip install camelot-py")

try:
    from pdfminer.high_level import extract_text as pdfminer_extract_text
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False
    print("警告: pdfminer.six不可用，备选PDF提取方法将不可用: pip install pdfminer.six")


def process_page(page_info):
    """
    处理单个PDF页面，提取文本和表格，增强错误处理和数据验证
    优化表格文本提取，确保表格内容被正确处理

    参数:
        page_info (tuple): (page, page_index) 页面对象和页面索引

    返回:
        tuple: (page_text, page_tables, page_index) 页面文本、表格和索引
    """
    page, page_index = page_info
    page_text = ""
    page_tables = []

    try:
        # 提取页面文本，增加错误处理
        try:
            # 使用更优化的参数提取文本，针对金融研报特点调整参数
            extracted_text = page.extract_text(x_tolerance=2.5, y_tolerance=3)
            if extracted_text:
                # 清理文本中的特殊字符和多余空白
                extracted_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', extracted_text)
                # 保留段落结构，不将所有空白替换为单个空格
                extracted_text = re.sub(r' {2,}', ' ', extracted_text)
                # 保留换行符，以保持段落结构
                extracted_text = re.sub(r'\n{3,}', '\n\n', extracted_text)
                page_text = extracted_text + "\n"
                print(f"  成功提取第{page_index+1}页文本，长度: {len(extracted_text)}字符")
        except Exception as text_error:
            print(f"  警告: 第{page_index+1}页文本提取错误: {text_error}")
            # 尝试使用备选方法提取文本
            try:
                # 使用更宽松的参数重试
                extracted_text = page.extract_text(x_tolerance=5, y_tolerance=8)
                if extracted_text:
                    extracted_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', extracted_text)
                    extracted_text = re.sub(r' {2,}', ' ', extracted_text)
                    extracted_text = re.sub(r'\n{3,}', '\n\n', extracted_text)
                    page_text = extracted_text + "\n"
                    print(f"  使用备选方法成功提取第{page_index+1}页文本，长度: {len(extracted_text)}字符")
                else:
                    # 如果备选方法仍然无法提取文本，尝试使用更激进的参数
                    extracted_text = page.extract_text(x_tolerance=10, y_tolerance=12)
                    if extracted_text:
                        extracted_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', extracted_text)
                        extracted_text = re.sub(r' {2,}', ' ', extracted_text)
                        extracted_text = re.sub(r'\n{3,}', '\n\n', extracted_text)
                        page_text = extracted_text + "\n"
                        print(f"  使用激进参数成功提取第{page_index+1}页文本，长度: {len(extracted_text)}字符")
            except Exception as backup_error:
                print(f"  备选方法提取第{page_index+1}页文本也失败: {backup_error}")

        # 提取页面表格，增加错误处理和验证
        extracted_tables = []

        # 首先尝试检测页面中是否可能包含表格
        has_potential_tables = False
        try:
            # 检查页面是否包含表格线条
            if page.lines:
                horizontal_lines = [line for line in page.lines if abs(line["x1"] - line["x0"]) > abs(line["y1"] - line["y0"])]
                vertical_lines = [line for line in page.lines if abs(line["y1"] - line["y0"]) > abs(line["x1"] - line["x0"])]

                # 如果有足够的水平线和垂直线，可能存在表格
                if len(horizontal_lines) >= 3 and len(vertical_lines) >= 2:
                    has_potential_tables = True
                    print(f"  检测到第{page_index+1}页可能包含表格（{len(horizontal_lines)}条水平线，{len(vertical_lines)}条垂直线）")
        except Exception as detect_error:
            print(f"  表格检测过程出错: {detect_error}")

        # 根据检测结果选择不同的提取策略
        try:
            if has_potential_tables:
                # 使用基于线条的表格提取策略
                extracted_tables = page.extract_tables(table_settings={
                    "vertical_strategy": "lines",
                    "horizontal_strategy": "lines",
                    "intersection_tolerance": 5,
                    "snap_tolerance": 3,
                    "join_tolerance": 3,
                    "edge_min_length": 3
                })
            else:
                # 使用基于文本的表格提取策略
                extracted_tables = page.extract_tables(table_settings={
                    "vertical_strategy": "text",
                    "horizontal_strategy": "text",
                    "intersection_tolerance": 5,
                    "snap_tolerance": 3,
                    "join_tolerance": 3,
                    "edge_min_length": 3,
                    "min_words_vertical": 2,  # 确保垂直线至少有2个词
                    "min_words_horizontal": 2  # 确保水平线至少有2个词
                })

            if extracted_tables:
                print(f"  成功提取第{page_index+1}页表格，共 {len(extracted_tables)} 个表格")
            elif has_potential_tables:
                # 如果检测到可能有表格但提取失败，尝试使用更宽松的设置
                print(f"  检测到表格但提取失败，尝试使用更宽松的设置...")
                extracted_tables = page.extract_tables(table_settings={
                    "vertical_strategy": "lines_strict",
                    "horizontal_strategy": "lines_strict",
                    "intersection_tolerance": 10,
                    "snap_tolerance": 6,
                    "join_tolerance": 6,
                    "edge_min_length": 2
                })
                if extracted_tables:
                    print(f"  使用严格线条策略成功提取第{page_index+1}页表格，共 {len(extracted_tables)} 个表格")
        except Exception as table_extract_error:
            print(f"  警告: 第{page_index+1}页表格提取错误: {table_extract_error}")
            # 尝试使用多种备选方法
            try:
                # 方法1: 使用更宽松的线条策略
                extracted_tables = page.extract_tables(table_settings={
                    "vertical_strategy": "lines",
                    "horizontal_strategy": "lines",
                    "intersection_tolerance": 10,
                    "snap_tolerance": 6,
                    "join_tolerance": 6,
                    "edge_min_length": 2
                })
                if extracted_tables:
                    print(f"  使用备选方法1成功提取第{page_index+1}页表格，共 {len(extracted_tables)} 个表格")
                else:
                    # 方法2: 使用混合策略
                    extracted_tables = page.extract_tables(table_settings={
                        "vertical_strategy": "lines",
                        "horizontal_strategy": "text",
                        "intersection_tolerance": 10,
                        "snap_tolerance": 8,
                        "join_tolerance": 8,
                        "edge_min_length": 2
                    })
                    if extracted_tables:
                        print(f"  使用备选方法2成功提取第{page_index+1}页表格，共 {len(extracted_tables)} 个表格")
                    else:
                        # 方法3: 使用最宽松的设置
                        extracted_tables = page.extract_tables(table_settings={
                            "vertical_strategy": "text",
                            "horizontal_strategy": "text",
                            "intersection_tolerance": 15,
                            "snap_tolerance": 10,
                            "join_tolerance": 10,
                            "edge_min_length": 1,
                            "min_words_vertical": 1,
                            "min_words_horizontal": 1
                        })
                        if extracted_tables:
                            print(f"  使用备选方法3成功提取第{page_index+1}页表格，共 {len(extracted_tables)} 个表格")
            except Exception as backup_table_error:
                print(f"  所有备选方法提取第{page_index+1}页表格都失败: {backup_table_error}")

        table_texts = []

        for j, table in enumerate(extracted_tables):
            if table and len(table) > 1:  # 确保表格有内容且有标题行
                try:
                    # 验证表格数据有效性
                    valid_rows = 0
                    for row in table:
                        if any(cell is not None and str(cell).strip() for cell in row):
                            valid_rows += 1

                    if valid_rows < 2:  # 至少需要标题行和一行数据
                        print(f"  跳过第{page_index+1}页表格{j+1}：有效行数不足")
                        continue

                    # 处理表格标题行可能为None的情况
                    headers = [str(col).strip() if col is not None else f"列{k+1}" for k, col in enumerate(table[0])]

                    # 过滤掉全空的标题
                    if all(not h or h.isspace() for h in headers):
                        # 如果标题行全空，使用第一行非空数据作为标题
                        for row_idx, row in enumerate(table[1:], 1):
                            if any(cell is not None and str(cell).strip() for cell in row):
                                headers = [str(cell).strip() if cell is not None else f"列{k+1}"
                                          for k, cell in enumerate(row)]
                                table = table[:row_idx] + table[row_idx+1:]
                                print(f"  第{page_index+1}页表格{j+1}：使用第{row_idx+1}行作为标题")
                                break

                    # 处理表格数据行
                    data = []
                    for row in table[1:]:
                        # 处理单元格可能为None的情况
                        processed_row = [str(cell).strip() if cell is not None else "" for cell in row]

                        # 跳过全空行
                        if all(not cell or cell.isspace() for cell in processed_row):
                            continue

                        # 确保行长度与标题行一致
                        if len(processed_row) < len(headers):
                            processed_row.extend([""] * (len(headers) - len(processed_row)))
                        elif len(processed_row) > len(headers):
                            processed_row = processed_row[:len(headers)]

                        # 清理每个单元格的文本
                        processed_row = [re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', cell)
                                        for cell in processed_row]
                        processed_row = [re.sub(r'\s+', ' ', cell).strip() for cell in processed_row]

                        data.append(processed_row)

                    # 验证处理后的数据是否有效
                    if not data:
                        print(f"  跳过第{page_index+1}页表格{j+1}：处理后无有效数据")
                        continue

                    # 创建DataFrame
                    df = pd.DataFrame(data, columns=headers)

                    # 删除全空列
                    df = df.loc[:, ~df.apply(lambda x: x.astype(str).str.strip().eq('').all())]

                    # 如果DataFrame为空，跳过
                    if df.empty or df.shape[1] == 0:
                        print(f"  跳过第{page_index+1}页表格{j+1}：处理后为空表格")
                        continue

                    page_tables.append((df, page_index, j))
                    print(f"  成功处理第{page_index+1}页表格{j+1}：{df.shape[0]}行 x {df.shape[1]}列")

                    # 将表格内容也添加到文本中，确保表格文字被处理
                    table_text = f"\n表格{j+1}（第{page_index+1}页）:\n"
                    # 添加标题行
                    table_text += " | ".join(headers) + "\n"
                    # 添加分隔行
                    table_text += "-" * (sum(len(h) for h in headers) + 3 * (len(headers) - 1)) + "\n"
                    # 添加数据行
                    for row in data:
                        table_text += " | ".join(row) + "\n"

                    table_texts.append(table_text)

                except Exception as table_error:
                    print(f"  警告: 处理第{page_index+1}页表格{j+1}时出错: {table_error}")

        # 将表格文本添加到页面文本中
        if table_texts:
            page_text += "\n".join(table_texts) + "\n"
            print(f"  成功将{len(table_texts)}个表格的文本添加到第{page_index+1}页")

        # 验证提取的文本是否有效
        if page_text and len(page_text.strip()) > 0:
            return page_text, page_tables, page_index
        else:
            print(f"  警告: 第{page_index+1}页提取的文本为空，尝试使用OCR")
            # 如果提取的文本为空，尝试使用OCR（如果可用）
            if OCR_AVAILABLE:
                try:
                    # 将页面转换为图像
                    img = page.to_image(resolution=300)
                    pil_img = img.original

                    # 使用OCR提取文本
                    ocr_text = pytesseract.image_to_string(pil_img, lang='chi_sim+eng')
                    if ocr_text and len(ocr_text.strip()) > 0:
                        print(f"  使用OCR成功提取第{page_index+1}页文本，长度: {len(ocr_text)}字符")
                        return ocr_text + "\n", page_tables, page_index
                    else:
                        print(f"  OCR提取的第{page_index+1}页文本也为空")
                except Exception as ocr_error:
                    print(f"  OCR提取第{page_index+1}页文本失败: {ocr_error}")
            else:
                print(f"  OCR功能不可用，无法使用OCR提取第{page_index+1}页文本")

            # 如果OCR不可用或失败，尝试使用图像分析提取文本
            try:
                # 将页面转换为图像
                img = page.to_image(resolution=300)

                # 分析页面上的文本块
                words = page.extract_words(
                    x_tolerance=3,
                    y_tolerance=3,
                    keep_blank_chars=True,
                    use_text_flow=True,
                    horizontal_ltr=True,
                    vertical_ttb=True,
                    extra_attrs=["size", "font"]
                )

                if words:
                    # 按照垂直位置排序文本块
                    words.sort(key=lambda w: (w["top"], w["x0"]))

                    # 将文本块组合成文本
                    extracted_text = ""
                    current_line_top = words[0]["top"]
                    line_text = ""

                    for word in words:
                        # 如果垂直位置变化超过阈值，认为是新的一行
                        if abs(word["top"] - current_line_top) > word["size"] * 0.5:
                            extracted_text += line_text.strip() + "\n"
                            current_line_top = word["top"]
                            line_text = word["text"] + " "
                        else:
                            line_text += word["text"] + " "

                    # 添加最后一行
                    extracted_text += line_text.strip()

                    if extracted_text and len(extracted_text.strip()) > 0:
                        print(f"  使用文本块分析成功提取第{page_index+1}页文本，长度: {len(extracted_text)}字符")
                        return extracted_text + "\n", page_tables, page_index
                    else:
                        print(f"  文本块分析提取的第{page_index+1}页文本为空")
            except Exception as block_error:
                print(f"  文本块分析提取第{page_index+1}页文本失败: {block_error}")

            return page_text, page_tables, page_index

    except Exception as e:
        print(f"  错误: 处理第{page_index+1}页时出错: {e}")
        return "", [], page_index


def extract_text_with_pymupdf(pdf_path, silent=False):
    """
    使用PyMuPDF (fitz) 提取PDF文本，这是一个高性能的PDF处理库

    参数:
        pdf_path (str): PDF文件路径
        silent (bool): 是否静默处理（不打印进度信息）

    返回:
        tuple: (text, page_texts) 完整文本和按页分割的文本列表
    """
    if not PYMUPDF_AVAILABLE:
        if not silent:
            print("PyMuPDF不可用，无法使用此方法提取文本")
        return None, []

    try:
        if not silent:
            print("使用PyMuPDF提取PDF文本...")

        # 打开PDF文件
        doc = fitz.open(pdf_path)
        total_pages = len(doc)

        if not silent:
            print(f"PDF文件共 {total_pages} 页")

        all_text = ""
        page_texts = []

        # 遍历每一页
        for page_num in tqdm(range(total_pages), desc="PyMuPDF处理页面", disable=silent):
            page = doc[page_num]

            # 提取文本 - 使用不同的文本提取模式
            # TEXT_PRESERVE_LIGATURES - 保留连字
            # TEXT_PRESERVE_WHITESPACE - 保留空白
            # TEXT_PRESERVE_IMAGES - 包含图像信息
            text = page.get_text("text", sort=True)

            # 如果文本为空或太短，尝试其他模式
            if not text or len(text.strip()) < 50:
                # 尝试使用HTML模式，可能会保留更多格式信息
                text = page.get_text("html")
                # 从HTML中提取纯文本
                if text:
                    text = re.sub(r'<[^>]+>', ' ', text)
                    text = re.sub(r'&[^;]+;', ' ', text)
                    text = re.sub(r'\s+', ' ', text).strip()

            # 如果仍然为空，尝试使用JSON模式，提供更详细的文本块信息
            if not text or len(text.strip()) < 50:
                json_data = page.get_text("json")
                try:
                    blocks = json.loads(json_data)["blocks"]
                    text = ""
                    for block in blocks:
                        if "lines" in block:
                            for line in block["lines"]:
                                if "spans" in line:
                                    for span in line["spans"]:
                                        if "text" in span:
                                            text += span["text"] + " "
                            text += "\n"
                except Exception as json_error:
                    if not silent:
                        print(f"  JSON解析错误: {json_error}")

            # 清理文本
            if text:
                text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)
                text = re.sub(r' {2,}', ' ', text)
                text = re.sub(r'\n{3,}', '\n\n', text)

                all_text += text + "\n"
                page_texts.append(text)

                if not silent:
                    print(f"  成功提取第{page_num+1}页文本，长度: {len(text)}字符")
            else:
                if not silent:
                    print(f"  警告: 第{page_num+1}页未提取到文本")
                page_texts.append("")

        # 关闭文档
        doc.close()

        return all_text, page_texts

    except Exception as e:
        if not silent:
            print(f"PyMuPDF提取文本失败: {e}")
        return None, []


def extract_tables_with_camelot(pdf_path, pages='all', silent=False):
    """
    使用camelot-py提取PDF表格，这是一个专门用于表格提取的库

    参数:
        pdf_path (str): PDF文件路径
        pages (str): 要处理的页面，默认为'all'
        silent (bool): 是否静默处理（不打印进度信息）

    返回:
        list: 提取的表格列表
    """
    if not CAMELOT_AVAILABLE:
        if not silent:
            print("camelot-py不可用，无法使用此方法提取表格")
        return []

    try:
        if not silent:
            print("使用camelot-py提取PDF表格...")

        # 使用lattice方法提取表格（基于线条）
        tables_lattice = camelot.read_pdf(
            pdf_path,
            pages=pages,
            flavor='lattice',
            suppress_stdout=silent
        )

        if not silent:
            print(f"使用lattice方法提取了 {len(tables_lattice)} 个表格")

        # 使用stream方法提取表格（基于空间分析）
        tables_stream = camelot.read_pdf(
            pdf_path,
            pages=pages,
            flavor='stream',
            suppress_stdout=silent
        )

        if not silent:
            print(f"使用stream方法提取了 {len(tables_stream)} 个表格")

        # 合并结果，优先使用质量更高的表格
        all_tables = []

        # 处理lattice表格
        for i, table in enumerate(tables_lattice):
            if table.parsing_report['accuracy'] > 80:  # 只保留高质量表格
                df = table.df
                if not df.empty and df.shape[0] > 1 and df.shape[1] > 1:
                    all_tables.append((df, table.page, i))
                    if not silent:
                        print(f"  保留lattice表格 {i+1}，页面 {table.page}，准确率 {table.parsing_report['accuracy']:.1f}%")

        # 处理stream表格，避免重复
        for i, table in enumerate(tables_stream):
            if table.parsing_report['accuracy'] > 80:  # 只保留高质量表格
                # 检查是否与已有表格重复
                df = table.df
                is_duplicate = False

                for existing_df, existing_page, _ in all_tables:
                    if existing_page == table.page and df.shape == existing_df.shape:
                        # 简单比较表格大小和内容
                        if df.equals(existing_df) or df.values.tolist() == existing_df.values.tolist():
                            is_duplicate = True
                            break

                if not is_duplicate and not df.empty and df.shape[0] > 1 and df.shape[1] > 1:
                    all_tables.append((df, table.page, i))
                    if not silent:
                        print(f"  保留stream表格 {i+1}，页面 {table.page}，准确率 {table.parsing_report['accuracy']:.1f}%")

        return all_tables

    except Exception as e:
        if not silent:
            print(f"camelot-py提取表格失败: {e}")
        return []


def extract_text_and_tables_from_pdf(pdf_path, max_workers=4, silent=False):
    """
    从PDF文件中提取文本和表格数据，支持多种提取方法和并行处理
    优化版本：确保PDF内容完整提取，包括文本和表格

    参数:
        pdf_path (str): PDF文件路径
        max_workers (int): 并行处理的最大工作线程数
        silent (bool): 是否静默处理（不打印进度信息）

    返回:
        tuple: (text, tables) 提取的文本内容和表格数据列表
    """
    start_time = time.time()

    if not silent:
        print(f"正在处理PDF文件: {pdf_path}")

    # 1. 首先尝试使用PyMuPDF提取文本（通常效果最好）
    pymupdf_text = None
    pymupdf_page_texts = []

    if PYMUPDF_AVAILABLE:
        pymupdf_text, pymupdf_page_texts = extract_text_with_pymupdf(pdf_path, silent)

        if pymupdf_text and len(pymupdf_text.strip()) > 100:
            if not silent:
                print(f"PyMuPDF成功提取文本，共 {len(pymupdf_text)} 个字符")

    # 2. 使用pdfplumber提取文本和表格（作为备选或补充）
    pdfplumber_text = ""
    pdfplumber_tables = []

    try:
        if not silent:
            print(f"使用pdfplumber处理PDF文件...")

        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            if not silent:
                print(f"PDF文件共 {total_pages} 页")

            # 准备页面处理任务
            page_infos = [(page, i) for i, page in enumerate(pdf.pages)]

            # 使用线程池并行处理页面
            all_text = []
            all_tables = []

            # 根据页面数量调整工作线程数
            actual_workers = min(max_workers, total_pages)

            with concurrent.futures.ThreadPoolExecutor(max_workers=actual_workers) as executor:
                # 使用tqdm显示进度条
                if not silent:
                    results = list(tqdm(executor.map(process_page, page_infos), total=total_pages, desc="pdfplumber处理页面"))
                else:
                    results = list(executor.map(process_page, page_infos))

            # 按页面顺序整理结果
            sorted_results = sorted(results, key=lambda x: x[2])

            # 合并文本和表格
            for page_text, page_tables, _ in sorted_results:
                all_text.append(page_text)
                all_tables.extend([table for table, _, _ in page_tables])

            # 合并所有文本
            pdfplumber_text = "".join(all_text)
            pdfplumber_tables = all_tables

            # 清理文本中的多余空白字符，但保留段落结构
            pdfplumber_text = re.sub(r' {2,}', ' ', pdfplumber_text)
            pdfplumber_text = re.sub(r'\n{3,}', '\n\n', pdfplumber_text)

            if not silent:
                print(f"pdfplumber成功提取文本，共 {len(pdfplumber_text)} 个字符")
                print(f"pdfplumber成功提取表格，共 {len(pdfplumber_tables)} 个表格")

    except Exception as e:
        if not silent:
            print(f"pdfplumber处理PDF文件时出错: {e}")

    # 3. 使用camelot-py专门提取表格（通常表格提取效果最好）
    camelot_tables = []

    if CAMELOT_AVAILABLE:
        camelot_tables = extract_tables_with_camelot(pdf_path, silent=silent)

        if camelot_tables and len(camelot_tables) > 0:
            if not silent:
                print(f"camelot-py成功提取表格，共 {len(camelot_tables)} 个表格")

    # 4. 如果前面的方法都失败，尝试使用PyPDF2和pdfminer.six
    pypdf2_text = ""
    pdfminer_text = ""

    if (not pymupdf_text or len(pymupdf_text.strip()) < 100) and (not pdfplumber_text or len(pdfplumber_text.strip()) < 100):
        # 尝试PyPDF2
        if PYPDF2_AVAILABLE:
            try:
                if not silent:
                    print("尝试使用PyPDF2提取文本...")

                with open(pdf_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    total_pages = len(reader.pages)

                    if not silent:
                        for page_num in tqdm(range(total_pages), desc="PyPDF2处理页面"):
                            page = reader.pages[page_num]
                            page_text = page.extract_text()
                            if page_text:
                                pypdf2_text += page_text + "\n"
                    else:
                        for page_num in range(total_pages):
                            page = reader.pages[page_num]
                            page_text = page.extract_text()
                            if page_text:
                                pypdf2_text += page_text + "\n"

                # 清理文本
                pypdf2_text = re.sub(r' {2,}', ' ', pypdf2_text)
                pypdf2_text = re.sub(r'\n{3,}', '\n\n', pypdf2_text)

                if not silent:
                    print(f"PyPDF2成功提取文本，共 {len(pypdf2_text)} 个字符")

            except Exception as pypdf2_error:
                if not silent:
                    print(f"PyPDF2提取失败: {pypdf2_error}")

        # 尝试pdfminer.six
        if PDFMINER_AVAILABLE:
            try:
                if not silent:
                    print("尝试使用pdfminer.six提取文本...")

                pdfminer_text = pdfminer_extract_text(pdf_path)

                # 清理文本
                pdfminer_text = re.sub(r' {2,}', ' ', pdfminer_text)
                pdfminer_text = re.sub(r'\n{3,}', '\n\n', pdfminer_text)

                if not silent:
                    print(f"pdfminer.six成功提取文本，共 {len(pdfminer_text)} 个字符")

            except Exception as pdfminer_error:
                if not silent:
                    print(f"pdfminer.six提取失败: {pdfminer_error}")

    # 5. 整合所有提取结果，选择最佳文本和表格
    # 选择最长的文本作为最终结果
    text_candidates = [
        (pymupdf_text, "PyMuPDF"),
        (pdfplumber_text, "pdfplumber"),
        (pypdf2_text, "PyPDF2"),
        (pdfminer_text, "pdfminer.six")
    ]

    # 过滤掉空文本
    text_candidates = [(t, name) for t, name in text_candidates if t and len(t.strip()) > 0]

    # 按文本长度排序
    text_candidates.sort(key=lambda x: len(x[0]), reverse=True)

    if text_candidates:
        best_text, best_method = text_candidates[0]
        if not silent:
            print(f"选择 {best_method} 提取的文本作为最终结果，长度: {len(best_text)} 字符")
    else:
        best_text = ""
        if not silent:
            print("警告: 所有方法都未能提取到有效文本")

    # 合并表格结果，优先使用camelot-py的结果
    all_tables = []

    if camelot_tables:
        all_tables.extend(camelot_tables)
        if not silent:
            print(f"使用camelot-py提取的 {len(camelot_tables)} 个表格")

    # 如果camelot没有提取到足够的表格，补充使用pdfplumber的结果
    if len(all_tables) < 3 and pdfplumber_tables:
        # 检查是否有重复
        for table, page, idx in pdfplumber_tables:
            is_duplicate = False
            for existing_table, existing_page, _ in all_tables:
                if existing_page == page and table.shape == existing_table.shape:
                    # 简单比较表格大小和内容
                    if table.equals(existing_table) or table.values.tolist() == existing_table.values.tolist():
                        is_duplicate = True
                        break

            if not is_duplicate:
                all_tables.append((table, page, idx))

        if not silent:
            print(f"补充使用pdfplumber提取的表格，总计: {len(all_tables)} 个表格")

    # 6. 确保表格内容也被包含在文本中
    table_text = ""
    for table, page, _ in all_tables:
        table_text += f"\n表格（第{page+1}页）:\n"
        table_text += table.to_string(index=False) + "\n\n"

    # 检查表格文本是否已经包含在最终文本中
    if table_text and not table_text in best_text:
        best_text += "\n\n" + table_text
        if not silent:
            print(f"将 {len(all_tables)} 个表格的文本添加到最终结果中")

    # 7. 最终清理和验证
    # 清理文本中的特殊字符和多余空白
    best_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', best_text)
    best_text = re.sub(r' {2,}', ' ', best_text)
    best_text = re.sub(r'\n{3,}', '\n\n', best_text)

    # 验证提取的文本是否有效
    if not best_text or len(best_text.strip()) < 100:
        if not silent:
            print("警告: 提取的文本内容较少，可能不完整")

    end_time = time.time()
    if not silent:
        print(f"PDF处理完成，总耗时: {end_time - start_time:.2f} 秒")
        print(f"最终文本长度: {len(best_text)} 字符，表格数量: {len(all_tables)} 个")

    return best_text, all_tables


def get_table_text(tables):
    """
    从表格列表中提取所有文本内容

    参数:
        tables (list): 表格DataFrame列表

    返回:
        str: 表格中的所有文本内容
    """
    table_text = ""

    for df in tables:
        # 将DataFrame转换为字符串并添加到表格文本中
        table_text += df.to_string(index=False) + "\n\n"

    return table_text
