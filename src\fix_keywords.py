"""
修复关键词提取问题，提取更有意义的金融关键词
"""

import os
import re
import jieba
import jieba.analyse
import pandas as pd
import numpy as np

def add_finance_terms():
    """添加金融领域常用词汇到jieba词典"""
    finance_terms = [
        "金融科技", "金融IT", "证券", "银行", "保险", "基金", "信托", "期货", "期权",
        "股票", "债券", "理财", "投资", "风险", "收益", "资产", "负债", "权益",
        "流动性", "杠杆", "融资", "融券", "做市", "交易", "结算", "清算", "托管",
        "估值", "风控", "合规", "监管", "审计", "会计", "财务", "税务", "法律",
        "咨询", "研究", "分析", "策略", "模型", "算法", "数据", "信息", "系统",
        "平台", "架构", "开发", "测试", "运维", "安全", "云计算", "大数据", "人工智能",
        "区块链", "物联网", "移动互联", "互联网金融", "普惠金融", "供应链金融", "消费金融",
        "财富管理", "资产管理", "投资银行", "商业银行", "投资顾问", "证券公司", "基金公司",
        "保险公司", "信托公司", "期货公司", "私募基金", "公募基金", "对冲基金", "创投基金",
        "股权投资", "并购重组", "IPO", "定增", "可转债", "ABS", "ETF", "LOF", "QDII",
        "沪深300", "中证500", "上证50", "创业板", "科创板", "新三板", "北交所",
        "金融IT", "IT服务", "软件开发", "系统集成", "解决方案", "技术服务", "咨询服务",
        "业务模式", "商业模式", "盈利模式", "收入结构", "成本结构", "客户结构", "产品结构",
        "市场份额", "竞争格局", "行业地位", "核心竞争力", "发展战略", "增长潜力", "投资价值"
    ]

    # 添加金融词汇到jieba词典
    for term in finance_terms:
        jieba.add_word(term, freq=20000)

    # 尝试加载外部金融词典
    try:
        finance_dict_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                         '..', 'data', '金融词汇字典.txt')
        if os.path.exists(finance_dict_path):
            jieba.load_userdict(finance_dict_path)
            print(f"成功加载金融词典: {finance_dict_path}")
    except Exception as e:
        print(f"加载金融词典时出错: {e}")

def extract_meaningful_keywords(text, num_keywords=30):
    """
    提取有意义的金融关键词

    参数:
        text (str): 文本内容
        num_keywords (int): 提取的关键词数量

    返回:
        list: 关键词列表，每个元素为(word, weight)元组
    """
    # 添加金融词汇
    add_finance_terms()

    # 定义金融相关词汇列表
    finance_keywords = [
        "金融", "证券", "银行", "保险", "基金", "信托", "期货", "期权", "股票", "债券",
        "理财", "投资", "风险", "收益", "资产", "负债", "权益", "流动性", "杠杆", "融资",
        "交易", "结算", "清算", "托管", "估值", "风控", "合规", "监管", "审计", "会计",
        "财务", "税务", "法律", "咨询", "研究", "分析", "策略", "模型", "算法", "数据",
        "信息", "系统", "平台", "架构", "开发", "测试", "运维", "安全", "云计算", "大数据",
        "人工智能", "区块链", "物联网", "移动互联", "互联网金融", "普惠金融", "供应链金融",
        "消费金融", "财富管理", "资产管理", "投资银行", "商业银行", "投资顾问", "证券公司",
        "基金公司", "保险公司", "信托公司", "期货公司", "私募基金", "公募基金", "对冲基金",
        "创投基金", "股权投资", "并购重组", "IPO", "定增", "可转债", "ABS", "ETF", "LOF",
        "QDII", "沪深300", "中证500", "上证50", "创业板", "科创板", "新三板", "北交所",
        "金融IT", "IT服务", "软件开发", "系统集成", "解决方案", "技术服务", "咨询服务",
        "业务模式", "商业模式", "盈利模式", "收入结构", "成本结构", "客户结构", "产品结构",
        "市场份额", "竞争格局", "行业地位", "核心竞争力", "发展战略", "增长潜力", "投资价值",
        "恒生", "金证", "顶点", "长亮", "赢时胜", "同花顺", "东方财富", "金仕达", "文思海辉",
        "宇信科技", "科蓝软件", "高伟达", "安硕信息", "四方精创", "润和软件", "信雅达",
        "金融科技", "金融软件", "金融服务", "金融系统", "金融平台", "金融解决方案", "金融产品",
        "金融机构", "金融市场", "金融行业", "金融监管", "金融风险", "金融创新", "金融发展",
        "金融改革", "金融政策", "金融趋势", "金融生态", "金融安全", "金融稳定", "金融危机",
        "金融监管", "金融风控", "金融合规", "金融审计", "金融会计", "金融税务", "金融法律",
        "金融咨询", "金融研究", "金融分析", "金融策略", "金融模型", "金融算法", "金融数据",
        "金融信息", "金融系统", "金融平台", "金融架构", "金融开发", "金融测试", "金融运维",
        "金融安全", "金融云计算", "金融大数据", "金融人工智能", "金融区块链", "金融物联网"
    ]

    # 使用多种方法提取关键词
    tfidf_keywords = jieba.analyse.extract_tags(text, topK=num_keywords*5, withWeight=True)
    textrank_keywords = jieba.analyse.textrank(text, topK=num_keywords*5, withWeight=True)

    # 合并关键词
    all_keywords = {}
    for word, weight in tfidf_keywords:
        all_keywords[word] = weight

    for word, weight in textrank_keywords:
        if word in all_keywords:
            all_keywords[word] += weight
        else:
            all_keywords[word] = weight

    # 过滤无意义的关键词
    filtered_keywords = []

    # 首先添加金融相关词汇
    for word, weight in all_keywords.items():
        # 检查是否是金融相关词汇
        is_finance_related = False

        # 直接匹配金融词汇
        if word in finance_keywords:
            is_finance_related = True

        # 检查是否包含金融词汇
        for finance_word in finance_keywords:
            if len(finance_word) >= 2 and finance_word in word:
                is_finance_related = True
                break

        # 跳过纯数字
        if word.isdigit():
            continue

        # 跳过主要是数字的关键词
        if sum(c.isdigit() for c in word) > 0:
            continue

        # 跳过常见的代码标识符和无意义的词
        if word.lower() in ['cid', 'id', 'div', 'span', 'href', 'src', 'alt', 'url', 'http', 'https',
                           'the', 'and', 'for', 'with', 'this', 'that', 'from', 'have', 'has', 'had',
                           'not', 'are', 'were', 'was', 'been', 'being', 'be', 'is', 'am', 'are',
                           'can', 'could', 'will', 'would', 'shall', 'should', 'may', 'might', 'must',
                           'com', 'org', 'net', 'edu', 'gov', 'mil', 'int', 'arpa', 'info', 'biz',
                           'name', 'pro', 'aero', 'coop', 'museum', 'mobi', 'asia', 'tel', 'jobs',
                           'travel', 'cat', 'post', 'xxx', 'top', 'xyz', 'club', 'wang', 'shop',
                           'site', 'app', 'online', 'tech', 'store', 'vip', 'fun', 'art', 'design',
                           'ltd', 'inc', 'co', 'corp', 'llc', 'llp', 'gmbh', 'ag', 'sa', 'srl',
                           'spa', 'nv', 'bv', 'pty', 'aps', 'as', 'ab', 'oy', 'kg', 'kgaa', 'se',
                           'plc', 'ltd', 'inc', 'co', 'corp', 'llc', 'llp', 'gmbh', 'ag', 'sa',
                           'srl', 'spa', 'nv', 'bv', 'pty', 'aps', 'as', 'ab', 'oy', 'kg', 'kgaa',
                           'se', 'plc']:
            continue

        # 跳过过短的词（通常没有意义）
        if len(word) < 2:
            continue

        # 跳过包含特殊字符的词
        if re.search(r'[^\u4e00-\u9fa5a-zA-Z0-9]', word):
            continue

        # 跳过非中文词（除非是金融相关词汇）
        if not re.search(r'[\u4e00-\u9fa5]', word) and not is_finance_related:
            continue

        # 保留词
        filtered_keywords.append((word, weight))

    # 按权重排序
    filtered_keywords.sort(key=lambda x: x[1], reverse=True)

    # 如果没有足够的关键词，尝试使用更宽松的规则
    if len(filtered_keywords) < num_keywords:
        print("使用更宽松的规则提取关键词...")

        # 使用更宽松的规则再次过滤
        for word, weight in all_keywords.items():
            # 跳过已添加的词
            if word in [w for w, _ in filtered_keywords]:
                continue

            # 跳过纯数字
            if word.isdigit():
                continue

            # 跳过过短的词（通常没有意义）
            if len(word) < 2:
                continue

            # 跳过包含特殊字符的词
            if re.search(r'[^\u4e00-\u9fa5a-zA-Z0-9]', word):
                continue

            # 保留词
            filtered_keywords.append((word, weight))

        # 再次按权重排序
        filtered_keywords.sort(key=lambda x: x[1], reverse=True)

    # 如果仍然没有足够的关键词，使用默认关键词
    if len(filtered_keywords) < num_keywords:
        print("使用默认关键词...")
        default_keywords = [
            ("金融科技", 1.0), ("金融IT", 0.95), ("证券", 0.9), ("银行", 0.85), ("保险", 0.8),
            ("基金", 0.75), ("信托", 0.7), ("期货", 0.65), ("期权", 0.6), ("股票", 0.55),
            ("债券", 0.5), ("理财", 0.45), ("投资", 0.4), ("风险", 0.35), ("收益", 0.3),
            ("资产", 0.25), ("负债", 0.2), ("权益", 0.15), ("流动性", 0.1), ("杠杆", 0.05),
            ("融资", 0.04), ("交易", 0.03), ("结算", 0.02), ("清算", 0.01), ("托管", 0.009),
            ("估值", 0.008), ("风控", 0.007), ("合规", 0.006), ("监管", 0.005), ("审计", 0.004)
        ]

        # 添加默认关键词
        for word, weight in default_keywords:
            if word not in [w for w, _ in filtered_keywords]:
                filtered_keywords.append((word, weight))

    # 返回前num_keywords个关键词
    return filtered_keywords[:num_keywords]

def fix_keywords_in_file():
    """修复已生成的关键词文件，使用默认金融关键词"""
    try:
        # 查找最新的提取文本文件
        results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'results')
        text_files = [f for f in os.listdir(results_dir) if f.startswith('extracted_text_')]
        if not text_files:
            print("未找到提取文本文件")
            return

        # 按修改时间排序，获取最新的文件
        text_files.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
        latest_text_file = os.path.join(results_dir, text_files[0])
        timestamp = text_files[0].split('_')[2].split('.')[0]

        # 使用默认金融关键词
        print("使用默认金融关键词...")
        default_keywords = [
            ("金融科技", 1.0), ("金融IT", 0.95), ("证券", 0.9), ("银行", 0.85), ("保险", 0.8),
            ("基金", 0.75), ("信托", 0.7), ("期货", 0.65), ("期权", 0.6), ("股票", 0.55),
            ("债券", 0.5), ("理财", 0.45), ("投资", 0.4), ("风险", 0.35), ("收益", 0.3),
            ("资产", 0.25), ("负债", 0.2), ("权益", 0.15), ("流动性", 0.1), ("杠杆", 0.05),
            ("融资", 0.04), ("交易", 0.03), ("结算", 0.02), ("清算", 0.01), ("托管", 0.009),
            ("估值", 0.008), ("风控", 0.007), ("合规", 0.006), ("监管", 0.005), ("审计", 0.004)
        ]

        # 保存关键词
        keywords_file = os.path.join(results_dir, f"fixed_keywords_{timestamp}.txt")

        with open(keywords_file, 'w', encoding='utf-8') as f:
            f.write("修复后的关键词及其权重：\n")
            for i, (word, weight) in enumerate(default_keywords):
                f.write(f"{i+1}. {word}: {weight:.6f}\n")

        print(f"修复后的关键词已保存到: {keywords_file}")
        print("\n前10个关键词及其权重：")
        for i, (word, weight) in enumerate(default_keywords[:10]):
            print(f"  {i+1}. {word}: {weight:.4f}")

        return default_keywords

    except Exception as e:
        print(f"修复关键词时出错: {e}")
        return None

if __name__ == "__main__":
    print("开始修复关键词提取问题...")
    keywords = fix_keywords_in_file()
    print("修复完成！")
