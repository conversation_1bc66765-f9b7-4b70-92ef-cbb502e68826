"""
研报情感分析主程序
整合所有模块，实现研报文本提取、预处理、关键词提取、情感分析和可视化
"""

import os
import sys
import argparse
import pandas as pd
import time
from datetime import datetime

# 仅在需要时导入matplotlib
try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端，避免GUI相关问题
    import matplotlib.pyplot as plt
except ImportError:
    print("警告: matplotlib未安装，可视化功能将不可用")
    plt = None

# 导入自定义模块
from pdf_extractor import extract_text_and_tables_from_pdf
from text_preprocessor import preprocess_text
from keyword_extractor import extract_keywords_textrank, extract_keywords_with_sentences
from sentiment_analyzer import load_sentiment_dict, sentiment_analysis_by_dict, sentiment_analysis_by_finbert, compare_sentiment_results
from visualizer import (
    visualize_sentiment_comparison,
    visualize_keyword_sentiment,
    visualize_sentiment_agreement,
    visualize_keyword_cloud,
    visualize_sentiment_distribution,
    visualize_sentiment_comparison_detailed
)


def parse_arguments():
    """
    解析命令行参数

    返回:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='研报情感分析工具')

    # 输入输出参数
    parser.add_argument('-f', '--file', type=str, help='PDF文件路径')
    parser.add_argument('-o', '--output', type=str, help='输出目录路径')

    # 关键词提取参数
    parser.add_argument('-k', '--keywords', type=int, default=30, help='提取的关键词数量 (默认: 30)')
    parser.add_argument('--keyword-method', type=str, choices=['auto', 'textrank4zh', 'jieba', 'tfidf', 'all'],
                        default='auto', help='关键词提取方法 (默认: auto)')
    parser.add_argument('--min-word-len', type=int, default=2, help='最小词长度 (默认: 2)')

    # 情感分析参数
    parser.add_argument('--sentiment-method', type=str, choices=['dict', 'finbert', 'both'],
                        default='both', help='情感分析方法 (默认: both)')

    # 可视化参数
    parser.add_argument('--no-vis', action='store_true', help='不生成可视化图表')
    parser.add_argument('--vis-format', type=str, choices=['png', 'jpg', 'pdf', 'svg'],
                        default='png', help='可视化图表格式 (默认: png)')

    # 性能参数
    parser.add_argument('--parallel', action='store_true', help='使用并行处理')
    parser.add_argument('--max-workers', type=int, default=4, help='最大工作线程数 (默认: 4)')
    parser.add_argument('--silent', action='store_true', help='静默模式，减少输出信息')

    # 调试参数
    parser.add_argument('--debug', action='store_true', help='启用调试模式')

    return parser.parse_args()


def main():
    """
    主函数，执行研报情感分析流程
    """
    # 解析命令行参数
    args = parse_arguments()

    # 获取当前脚本所在目录的上一级目录（项目根目录）
    root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # 设置文件路径
    # 如果用户指定了PDF文件路径，则使用用户指定的路径
    if args.file:
        pdf_path = args.file
        # 如果路径不是绝对路径，则转换为相对于当前目录的路径
        if not os.path.isabs(pdf_path):
            pdf_path = os.path.join(os.getcwd(), pdf_path)
    else:
        # 否则，查找data目录下的PDF文件
        pdf_dir = os.path.join(root_dir, 'data')
        pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
        if pdf_files:
            pdf_path = os.path.join(pdf_dir, pdf_files[0])
        else:
            pdf_path = os.path.join(root_dir, 'data', '2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf')

    # 设置其他文件路径
    stopwords_path = os.path.join(root_dir, 'data', 'stopwords.txt')

    # 尝试使用data目录下的金融词汇字典文件
    finance_dict_path = os.path.join(root_dir, 'data', '金融词汇字典.txt')

    # 尝试不同路径的正面词典和负面词典
    # 首先尝试直接在data目录下查找
    positive_dict_path = os.path.join(root_dir, 'data', '正面词典.txt')
    if not os.path.exists(positive_dict_path):
        # 如果不存在，尝试CFSD中文金融情感词典目录
        positive_dict_path = os.path.join(root_dir, 'data', 'CFSD中文金融情感词典', '正面词典.csv')
        # 如果仍不存在，尝试旧的路径
        if not os.path.exists(positive_dict_path):
            positive_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '正面词典.txt')
            if not os.path.exists(positive_dict_path):
                positive_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '2', '正面词典.txt')

    negative_dict_path = os.path.join(root_dir, 'data', '负面词典.txt')
    if not os.path.exists(negative_dict_path):
        # 如果不存在，尝试CFSD中文金融情感词典目录
        negative_dict_path = os.path.join(root_dir, 'data', 'CFSD中文金融情感词典', '负面词典.csv')
        # 如果仍不存在，尝试旧的路径
        if not os.path.exists(negative_dict_path):
            negative_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '负面词典.txt')
            if not os.path.exists(negative_dict_path):
                negative_dict_path = os.path.join(root_dir, 'data', '会计金融情绪词典', '2', '负面词典.txt')

    # 设置输出目录
    if args.output:
        results_dir = args.output
        # 如果路径不是绝对路径，则转换为相对于当前目录的路径
        if not os.path.isabs(results_dir):
            results_dir = os.path.join(os.getcwd(), results_dir)
    else:
        results_dir = os.path.join(root_dir, 'results')

    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 打印文件路径，方便调试
    print(f"PDF文件路径: {pdf_path}")
    print(f"停用词文件路径: {stopwords_path}")
    print(f"金融词汇字典文件路径: {finance_dict_path}")
    print(f"正面词典文件路径: {positive_dict_path}")
    print(f"负面词典文件路径: {negative_dict_path}")
    print(f"结果输出目录: {results_dir}")

    # 检查文件是否存在
    for path, name in [(pdf_path, "PDF文件"), (stopwords_path, "停用词文件"),
                       (finance_dict_path, "金融词汇字典"),
                       (positive_dict_path, "正面词典"), (negative_dict_path, "负面词典")]:
        if not os.path.exists(path):
            print(f"警告: {name}不存在: {path}")
            if name == "PDF文件":
                print("错误: PDF文件不存在，程序退出")
                sys.exit(1)

    print("="*50)
    print("研报情感分析系统")
    print("="*50)

    # 步骤1：从PDF文件中提取文本和表格
    print("\n1. 正在从PDF文件中提取文本和表格...")
    print("   使用优化版本的PDF提取器，确保内容完整提取...")

    # 使用并行处理提高性能
    max_workers = args.max_workers if args.parallel else 1
    silent = args.silent

    # 使用优化版本的PDF提取器，确保内容完整提取
    text, tables = extract_text_and_tables_from_pdf(pdf_path, max_workers=max_workers, silent=silent)

    # 验证提取结果
    if text is None or not text:
        print("错误：无法从PDF文件中提取文本。")
        return

    # 验证提取的文本质量
    if len(text.strip()) < 500:
        print("警告：提取的文本内容较少，可能不完整。")
        print("尝试使用更多提取方法组合...")

        # 再次尝试提取，使用不同的参数组合
        text, tables = extract_text_and_tables_from_pdf(pdf_path, max_workers=1, silent=False)

        if len(text.strip()) < 500:
            print("警告：多次尝试后仍未能提取足够的文本内容，分析结果可能不准确。")

    # 检查表格提取情况
    if not tables or len(tables) == 0:
        print("注意：未从PDF中提取到表格，或表格提取失败。")

    print(f"成功提取文本，共 {len(text)} 个字符")
    print(f"成功提取表格，共 {len(tables)} 个表格")

    # 保存提取的文本
    extracted_text_file = os.path.join(results_dir, f"extracted_text_{timestamp}.txt")
    with open(extracted_text_file, 'w', encoding='utf-8') as f:
        f.write(text)

    print(f"提取的文本已保存到: {extracted_text_file}")

    # 保存提取的表格
    if tables:
        extracted_tables_file = os.path.join(results_dir, f"extracted_tables_{timestamp}.xlsx")
        with pd.ExcelWriter(extracted_tables_file) as writer:
            for i, table_data in enumerate(tables):
                # 处理表格数据，确保是DataFrame对象
                if isinstance(table_data, tuple):
                    # 如果是元组，提取DataFrame
                    df = table_data[0]
                    if isinstance(df, pd.DataFrame):
                        df.to_excel(writer, sheet_name=f'Table_{i+1}', index=False)
                    else:
                        print(f"警告: 表格 {i+1} 不是有效的DataFrame，已跳过")
                elif isinstance(table_data, pd.DataFrame):
                    # 如果直接是DataFrame
                    table_data.to_excel(writer, sheet_name=f'Table_{i+1}', index=False)
                else:
                    print(f"警告: 表格 {i+1} 不是有效的DataFrame，已跳过")

        print(f"提取的表格已保存到: {extracted_tables_file}")

    # 如果是调试模式，保存更多信息
    if args.debug:
        debug_file = os.path.join(results_dir, f"extraction_debug_{timestamp}.txt")
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(f"PDF文件: {pdf_path}\n")
            f.write(f"提取时间: {timestamp}\n")
            f.write(f"并行处理: {args.parallel}\n")
            f.write(f"工作线程数: {max_workers}\n\n")

            f.write(f"提取的文本长度: {len(text)} 字符\n")
            f.write(f"提取的表格数量: {len(tables)} 个\n\n")

            f.write("文本样本（前500字符）:\n")
            f.write(text[:500] + "...\n\n")

            if tables and len(tables) > 0:
                f.write("表格样本（第一个表格）:\n")
                f.write(str(tables[0].head()) + "\n")

        print(f"提取调试信息已保存到: {debug_file}")

    # 步骤2：文本预处理
    print("\n2. 正在进行文本预处理...")
    filtered_words, filtered_text = preprocess_text(text, stopwords_path, finance_dict_path)

    if not filtered_words or not filtered_text:
        print("错误：文本预处理失败。")
        return

    print(f"成功完成文本预处理，处理后共 {len(filtered_words)} 个词语")

    # 保存预处理后的文本
    with open(f"{results_dir}/preprocessed_text_{timestamp}.txt", 'w', encoding='utf-8') as f:
        f.write(filtered_text)

    # 步骤3：关键词提取
    print("\n3. 正在使用TextRank算法提取关键词...")
    # 使用命令行参数指定的关键词数量和方法
    num_keywords = args.keywords
    min_word_len = args.min_word_len
    keyword_method = args.keyword_method

    if not args.silent:
        print(f"关键词提取参数: 数量={num_keywords}, 最小词长={min_word_len}, 方法={keyword_method}")

    # 尝试使用修复后的关键词
    try:
        # 查找最新的修复关键词文件
        fixed_keywords_files = [f for f in os.listdir(results_dir) if f.startswith('fixed_keywords_')]
        if fixed_keywords_files:
            # 按修改时间排序，获取最新的文件
            fixed_keywords_files.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
            latest_fixed_keywords_file = os.path.join(results_dir, fixed_keywords_files[0])

            print(f"使用修复后的关键词文件: {latest_fixed_keywords_file}")

            # 读取修复后的关键词
            fixed_keywords = []
            with open(latest_fixed_keywords_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[1:]:  # 跳过标题行
                    if line.strip():
                        parts = line.strip().split(': ')
                        if len(parts) == 2:
                            word = parts[0].split('. ')[1] if '. ' in parts[0] else parts[0]
                            weight = float(parts[1])
                            fixed_keywords.append((word, weight))

            if fixed_keywords:
                keywords = fixed_keywords
                print(f"成功加载修复后的关键词，共 {len(keywords)} 个关键词")
            else:
                # 如果无法读取修复后的关键词，使用默认方法提取
                print("无法读取修复后的关键词，使用默认方法提取...")
                keywords = extract_keywords_textrank(
                    filtered_text,
                    num_keywords=num_keywords,
                    word_min_len=min_word_len,
                    method=keyword_method
                )
        else:
            # 如果没有修复关键词文件，使用默认方法提取
            print("未找到修复后的关键词文件，使用默认方法提取...")
            keywords = extract_keywords_textrank(
                filtered_text,
                num_keywords=num_keywords,
                word_min_len=min_word_len,
                method=keyword_method
            )
    except Exception as e:
        print(f"加载修复后的关键词时出错: {e}")
        print("使用默认方法提取关键词...")
        keywords = extract_keywords_textrank(
            filtered_text,
            num_keywords=num_keywords,
            word_min_len=min_word_len,
            method=keyword_method
        )

    if not keywords:
        print("错误：关键词提取失败。")
        return

    if not args.silent:
        print(f"成功提取关键词，共 {len(keywords)} 个关键词")
        print("前10个关键词及其权重：")
        for i, (word, weight) in enumerate(keywords[:10]):
            print(f"  {i+1}. {word}: {weight:.4f}")

    # 提取包含关键词的代表性句子
    keyword_sentences = extract_keywords_with_sentences(text, keywords, num_sentences=2)

    # 保存关键词及其代表性句子
    keywords_file = os.path.join(results_dir, f"keywords_{timestamp}.txt")
    with open(keywords_file, 'w', encoding='utf-8') as f:
        f.write("关键词及其权重：\n")
        for word, weight in keywords:
            f.write(f"{word}: {weight:.4f}\n")

        f.write("\n关键词及其代表性句子：\n")
        for keyword, sentences in keyword_sentences.items():
            f.write(f"\n关键词: {keyword}\n")
            for i, sentence in enumerate(sentences):
                f.write(f"  句子{i+1}: {sentence}\n")

    if not args.silent:
        print(f"关键词及其代表性句子已保存到: {keywords_file}")

    # 如果是调试模式，保存更多信息
    if args.debug:
        debug_file = os.path.join(results_dir, f"keywords_debug_{timestamp}.txt")
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(f"关键词提取方法: {keyword_method}\n")
            f.write(f"关键词数量: {num_keywords}\n")
            f.write(f"最小词长: {min_word_len}\n\n")

            f.write("所有关键词及其权重：\n")
            for i, (word, weight) in enumerate(keywords):
                f.write(f"{i+1}. {word}: {weight:.6f}\n")

        if not args.silent:
            print(f"关键词调试信息已保存到: {debug_file}")

    # 步骤4：情感分析
    print("\n4. 正在进行情感分析...")

    # 获取情感分析方法
    sentiment_method = args.sentiment_method

    if not args.silent:
        print(f"情感分析方法: {sentiment_method}")

    # 初始化变量
    dict_score = 0
    dict_keywords = []
    matched_words = []
    finbert_score = 0
    finbert_keywords = []
    comparison_df = pd.DataFrame()
    agreement_rate = 0

    # 基于情感词典的情感分析
    if sentiment_method in ['dict', 'both']:
        if not args.silent:
            print("  4.1 正在加载情感词典...")

        sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)

        if not sentiment_dict:
            print("错误：情感词典加载失败。")
            if sentiment_method == 'dict':
                return
        else:
            if not args.silent:
                print(f"成功加载情感词典，共 {len(sentiment_dict)} 个情感词")

            if not args.silent:
                print("  4.2 正在使用情感词典进行情感分析...")

            start_time = time.time()
            dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(filtered_text, keywords, sentiment_dict)
            end_time = time.time()

            if not args.silent:
                print(f"基于情感词典的整体情感得分: {dict_score:.4f}")
                print(f"匹配到的情感词数量: {len(matched_words)}")
                print(f"情感词典分析耗时: {end_time - start_time:.2f} 秒")

    # 基于FinBERT的情感分析
    if sentiment_method in ['finbert', 'both']:
        if not args.silent:
            print("  4.3 正在使用FinBERT进行情感分析...")

        start_time = time.time()
        # 使用本地FinBERT模型进行情感分析
        finbert_score, finbert_keywords = sentiment_analysis_by_finbert(
            filtered_text,
            keywords,
            max_length=512
        )
        end_time = time.time()

        if not args.silent:
            print(f"基于FinBERT的整体情感得分: {finbert_score:.4f}")
            print(f"FinBERT分析耗时: {end_time - start_time:.2f} 秒")

            # 打印情感倾向
            sentiment = "正面" if finbert_score > 0.2 else "负面" if finbert_score < -0.2 else "中性"
            print(f"情感倾向: {sentiment}")

    # 比较两种情感分析方法的结果
    if sentiment_method == 'both':
        if not args.silent:
            print("  4.4 正在比较两种情感分析方法的结果...")

        comparison_df, agreement_rate, combined_score, sentiment_summary = compare_sentiment_results(
            dict_score, finbert_score, dict_keywords, finbert_keywords
        )

        if not args.silent:
            print(f"两种情感分析方法的一致率: {agreement_rate:.2%}")
            print(f"综合情感得分: {combined_score:.4f}")
            print(f"综合情感倾向: {sentiment_summary['整体情感分析']['综合结果']['情感倾向']}")

    # 保存情感分析结果
    sentiment_file = os.path.join(results_dir, f"sentiment_comparison_{timestamp}.xlsx")

    if sentiment_method == 'both':
        comparison_df.to_excel(sentiment_file, index=False)
    elif sentiment_method == 'dict':
        # 创建只包含情感词典结果的DataFrame
        dict_df = pd.DataFrame([
            {"关键词": word, "情感词典情感得分": score, "情感词典情感倾向": "正面" if score > 0 else "负面" if score < 0 else "中性", "词典权重": weight}
            for word, score, weight in dict_keywords
        ])
        dict_df.to_excel(sentiment_file, index=False)
    elif sentiment_method == 'finbert':
        # 创建只包含FinBERT结果的DataFrame
        finbert_df = pd.DataFrame([
            {"关键词": word, "FinBERT情感得分": score, "FinBERT情感倾向": "正面" if score > 0 else "负面" if score < 0 else "中性", "词典权重": weight}
            for word, score, weight in finbert_keywords
        ])
        finbert_df.to_excel(sentiment_file, index=False)

    if not args.silent:
        print(f"情感分析结果已保存到: {sentiment_file}")

    # 如果是调试模式，保存更多信息
    if args.debug:
        debug_file = os.path.join(results_dir, f"sentiment_debug_{timestamp}.txt")
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(f"情感分析方法: {sentiment_method}\n\n")

            if sentiment_method in ['dict', 'both']:
                f.write("情感词典分析结果:\n")
                f.write(f"整体情感得分: {dict_score:.6f}\n")
                f.write(f"匹配到的情感词数量: {len(matched_words)}\n\n")

                f.write("匹配到的情感词及其得分:\n")
                for word, score in matched_words:
                    sentiment = "正面" if score > 0 else "负面" if score < 0 else "中性"
                    f.write(f"{word}: {score:.4f} ({sentiment})\n")
                f.write("\n")

            if sentiment_method in ['finbert', 'both']:
                f.write("FinBERT分析结果:\n")
                f.write(f"整体情感得分: {finbert_score:.6f}\n\n")

            if sentiment_method == 'both':
                f.write(f"两种方法一致率: {agreement_rate:.2%}\n")
                f.write(f"综合情感得分: {combined_score:.6f}\n")
                f.write(f"综合情感倾向: {sentiment_summary['整体情感分析']['综合结果']['情感倾向']}\n\n")

                # 添加一致性统计信息
                f.write("一致性统计:\n")
                for level, rate in sentiment_summary["关键词情感分析"]["一致性统计"].items():
                    f.write(f"  {level}: {rate:.2%}\n")
                f.write("\n")

                # 添加情感分布信息
                f.write("情感分布:\n")
                for category, rate in sentiment_summary["情感分布"].items():
                    f.write(f"  {category}: {rate:.2%}\n")
                f.write("\n")

        if not args.silent:
            print(f"情感分析调试信息已保存到: {debug_file}")

    # 步骤5：可视化呈现
    if not args.no_vis:
        print("\n5. 正在进行可视化呈现...")

        # 检查可视化功能是否可用
        if plt is None:
            print("警告: matplotlib未正确加载，跳过可视化步骤")
            args.no_vis = True

        # 获取可视化格式
        vis_format = args.vis_format

        # 创建可视化目录
        vis_dir = os.path.join(results_dir, "visualizations")
        os.makedirs(vis_dir, exist_ok=True)

        # 可视化计数器
        vis_count = 0

        # 可视化两种情感分析方法的整体得分对比
        if sentiment_method == 'both':
            if not args.silent:
                print("  5.1 正在可视化两种情感分析方法的整体得分对比...")

            # 基本比较可视化
            sentiment_comparison_path = os.path.join(vis_dir, f"sentiment_comparison_{timestamp}.{vis_format}")
            visualize_sentiment_comparison(
                dict_score, finbert_score,
                save_path=sentiment_comparison_path
            )

            if not args.silent:
                print(f"    基本比较可视化已保存到: {sentiment_comparison_path}")

            # 详细比较可视化
            detailed_vis_dir = os.path.join(vis_dir, "detailed_comparison")
            os.makedirs(detailed_vis_dir, exist_ok=True)

            try:
                visualize_sentiment_comparison_detailed(
                    comparison_df,
                    agreement_rate,
                    combined_score,
                    sentiment_summary,
                    output_dir=detailed_vis_dir
                )
                if not args.silent:
                    print(f"    详细比较可视化已保存到: {detailed_vis_dir}")
            except Exception as e:
                print(f"    详细比较可视化生成失败: {e}")
                print("    继续执行其他可视化...")

            vis_count += 1

        # 可视化基于情感词典的关键词情感得分
        if sentiment_method in ['dict', 'both'] and dict_keywords:
            if not args.silent:
                print("  5.2 正在可视化基于情感词典的关键词情感得分...")

            dict_keyword_sentiment_path = os.path.join(vis_dir, f"dict_keyword_sentiment_{timestamp}.{vis_format}")
            visualize_keyword_sentiment(
                dict_keywords, "情感词典", top_n=min(15, len(dict_keywords)),
                save_path=dict_keyword_sentiment_path
            )

            if not args.silent:
                print(f"    已保存到: {dict_keyword_sentiment_path}")

            vis_count += 1

        # 可视化基于FinBERT的关键词情感得分
        if sentiment_method in ['finbert', 'both'] and finbert_keywords:
            if not args.silent:
                print("  5.3 正在可视化基于FinBERT的关键词情感得分...")

            finbert_keyword_sentiment_path = os.path.join(vis_dir, f"finbert_keyword_sentiment_{timestamp}.{vis_format}")
            visualize_keyword_sentiment(
                finbert_keywords, "FinBERT", top_n=min(15, len(finbert_keywords)),
                save_path=finbert_keyword_sentiment_path
            )

            if not args.silent:
                print(f"    已保存到: {finbert_keyword_sentiment_path}")

            vis_count += 1

        # 可视化两种情感分析方法的一致性
        if sentiment_method == 'both' and not comparison_df.empty:
            if not args.silent:
                print("  5.4 正在可视化两种情感分析方法的一致性...")

            sentiment_agreement_path = os.path.join(vis_dir, f"sentiment_agreement_{timestamp}.{vis_format}")
            visualize_sentiment_agreement(
                comparison_df,
                save_path=sentiment_agreement_path
            )

            if not args.silent:
                print(f"    已保存到: {sentiment_agreement_path}")

            vis_count += 1

        # 可视化关键词云图
        if not args.silent:
            print("  5.5 正在可视化关键词云图...")

        try:
            keyword_cloud_path = os.path.join(vis_dir, f"keyword_cloud_{timestamp}.{vis_format}")
            visualize_keyword_cloud(
                keywords,
                save_path=keyword_cloud_path
            )

            if not args.silent:
                print(f"    已保存到: {keyword_cloud_path}")

            vis_count += 1
        except Exception as e:
            if not args.silent:
                print(f"    词云可视化失败: {e}")

        # 可视化情感词分布
        if sentiment_method in ['dict', 'both'] and matched_words:
            if not args.silent:
                print("  5.6 正在可视化情感词分布...")

            sentiment_distribution_path = os.path.join(vis_dir, f"sentiment_distribution_{timestamp}.{vis_format}")
            visualize_sentiment_distribution(
                matched_words,
                save_path=sentiment_distribution_path
            )

            if not args.silent:
                print(f"    已保存到: {sentiment_distribution_path}")

            vis_count += 1

        if not args.silent:
            print(f"\n共生成 {vis_count} 个可视化图表，保存在: {vis_dir}")
    else:
        print("\n5. 跳过可视化呈现（--no-vis 参数已指定）")

    # 打印结果摘要
    print("\n分析结果摘要:")
    print(f"  - 提取的文本长度: {len(text)} 字符")
    print(f"  - 提取的表格数量: {len(tables)} 个")
    print(f"  - 关键词数量: {len(keywords)} 个")
    print(f"  - 情感词典分析得分: {dict_score:.4f} ({dict_score > 0 and '正面' or dict_score < 0 and '负面' or '中性'})")
    print(f"  - FinBERT分析得分: {finbert_score:.4f} ({finbert_score > 0 and '正面' or finbert_score < 0 and '负面' or '中性'})")
    print(f"  - 两种方法一致率: {agreement_rate:.2%}")

    print(f"\n分析完成！所有结果已保存到: {results_dir}")
    print("="*50)


if __name__ == "__main__":
    main()