"""
情感分析模块
实现基于情感词典和FinBERT的情感分析
"""

import os
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import pandas as pd
import numpy as np


def load_sentiment_dict(positive_dict_path, negative_dict_path):
    """
    加载情感词典，支持多种格式，优化金融领域情感词典
    支持TXT和CSV格式的词典文件

    参数:
        positive_dict_path (str): 正面情感词典文件路径
        negative_dict_path (str): 负面情感词典文件路径

    返回:
        dict: 情感词典，格式为 {word: score}
    """
    sentiment_dict = {}
    import os

    try:
        print("开始加载情感词典...")

        # 检查文件是否存在
        if not os.path.exists(positive_dict_path):
            print(f"警告: 正面情感词典文件不存在: {positive_dict_path}")
        else:
            # 根据文件扩展名选择加载方法
            file_ext = os.path.splitext(positive_dict_path)[1].lower()

            if file_ext == '.csv':
                # 加载CSV格式的正面情感词典
                print(f"加载CSV格式的正面情感词典: {positive_dict_path}")
                try:
                    import pandas as pd
                    df = pd.read_csv(positive_dict_path, encoding='utf-8')

                    # 检查CSV文件的列
                    if len(df.columns) >= 1:
                        # 假设第一列是词语
                        for word in df.iloc[:, 0]:
                            if isinstance(word, str) and word.strip() and len(word.strip()) > 1:
                                if any('\u4e00' <= char <= '\u9fff' for char in word):
                                    sentiment_dict[word.strip()] = 1.0

                    print(f"从CSV加载了 {sum(1 for v in sentiment_dict.values() if v > 0)} 个正面情感词")
                except Exception as e:
                    print(f"加载CSV格式的正面情感词典时出错: {e}")
            else:
                # 加载TXT格式的正面情感词典
                with open(positive_dict_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                    # 检测文件格式
                    if len(lines) > 5 and "Chinese" in ''.join(lines[:5]):
                        print("检测到标准格式的正面情感词典")
                        # 标准格式：每个英文词后面跟着一个中文词
                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue

                            # 检查是否是中文词
                            if any('\u4e00' <= char <= '\u9fff' for char in line):
                                # 这是一个中文词
                                word = line
                                if word and len(word) > 1:
                                    sentiment_dict[word] = 1.0
                            else:
                                # 这可能是一个英文词，忽略
                                pass
                    else:
                        print("使用简单格式加载正面情感词典")
                        # 简单格式：每行一个词
                        for line in lines:
                            word = line.strip()
                            if word and len(word) > 1 and any('\u4e00' <= char <= '\u9fff' for char in word):
                                sentiment_dict[word] = 1.0

        # 检查文件是否存在
        if not os.path.exists(negative_dict_path):
            print(f"警告: 负面情感词典文件不存在: {negative_dict_path}")
        else:
            # 根据文件扩展名选择加载方法
            file_ext = os.path.splitext(negative_dict_path)[1].lower()

            if file_ext == '.csv':
                # 加载CSV格式的负面情感词典
                print(f"加载CSV格式的负面情感词典: {negative_dict_path}")
                try:
                    import pandas as pd
                    df = pd.read_csv(negative_dict_path, encoding='utf-8')

                    # 检查CSV文件的列
                    if len(df.columns) >= 1:
                        # 假设第一列是词语
                        for word in df.iloc[:, 0]:
                            if isinstance(word, str) and word.strip() and len(word.strip()) > 1:
                                if any('\u4e00' <= char <= '\u9fff' for char in word):
                                    sentiment_dict[word.strip()] = -1.0

                    print(f"从CSV加载了 {sum(1 for v in sentiment_dict.values() if v < 0)} 个负面情感词")
                except Exception as e:
                    print(f"加载CSV格式的负面情感词典时出错: {e}")
            else:
                # 加载TXT格式的负面情感词典
                with open(negative_dict_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                    # 检测文件格式
                    if len(lines) > 5 and "Chinese" in ''.join(lines[:5]):
                        print("检测到标准格式的负面情感词典")
                        # 标准格式：每个英文词后面跟着一个中文词
                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue

                            # 检查是否是中文词
                            if any('\u4e00' <= char <= '\u9fff' for char in line):
                                # 这是一个中文词
                                word = line
                                if word and len(word) > 1:
                                    sentiment_dict[word] = -1.0
                            else:
                                # 这可能是一个英文词，忽略
                                pass
                    else:
                        print("使用简单格式加载负面情感词典")
                        # 简单格式：每行一个词
                        for line in lines:
                            word = line.strip()
                            if word and len(word) > 1 and any('\u4e00' <= char <= '\u9fff' for char in word):
                                sentiment_dict[word] = -1.0

        # 添加常用金融情感词
        print("添加金融领域专用情感词...")
        financial_sentiment_dict = {
            # 正面词 - 金融领域
            "增长": 1.0, "提高": 1.0, "上升": 1.0, "增加": 1.0, "提升": 1.0, "改善": 1.0,
            "优化": 1.0, "创新": 1.0, "领先": 1.0, "突破": 1.0, "机遇": 1.0, "机会": 1.0,
            "优势": 1.0, "强劲": 1.0, "稳健": 1.0, "稳定": 1.0, "成功": 1.0, "盈利": 1.0,
            "收益": 1.0, "利润": 1.0, "回报": 1.0, "效益": 1.0, "效率": 1.0, "效果": 1.0,
            "发展": 1.0, "扩张": 1.0, "扩大": 1.0, "拓展": 1.0, "延伸": 1.0, "延展": 1.0,
            "推进": 1.0, "推动": 1.0, "促进": 1.0, "加速": 1.0, "加快": 1.0, "加强": 1.0,
            "支持": 1.0, "支撑": 1.0, "支援": 1.0, "帮助": 1.0, "协助": 1.0, "协同": 1.0,
            "合作": 1.0, "合力": 1.0, "合规": 1.0, "合理": 1.0, "合法": 1.0, "合适": 1.0,
            "利好": 1.0, "向好": 1.0, "看好": 1.0, "看涨": 1.0, "看多": 1.0, "看高": 1.0,
            "升值": 1.0, "增值": 1.0, "升高": 1.0, "升级": 1.0, "提速": 1.0, "提质": 1.0,
            "增效": 1.0, "增收": 1.0, "增益": 1.0, "增盈": 1.0, "增利": 1.0, "增值": 1.0,
            "盈余": 1.0, "盈余增长": 1.0, "盈利增长": 1.0, "盈利能力": 1.0, "盈利水平": 1.0,
            "高增长": 1.0, "高回报": 1.0, "高收益": 1.0, "高利润": 1.0, "高价值": 1.0,
            "业绩增长": 1.0, "业绩提升": 1.0, "业绩向好": 1.0, "业绩优良": 1.0, "业绩优异": 1.0,
            "市场份额": 1.0, "市场占有率": 1.0, "市场地位": 1.0, "市场优势": 1.0, "市场领先": 1.0,
            "竞争优势": 1.0, "竞争力": 1.0, "核心竞争力": 1.0, "比较优势": 1.0, "先发优势": 1.0,
            "技术优势": 1.0, "技术领先": 1.0, "技术创新": 1.0, "技术突破": 1.0, "技术进步": 1.0,

            # 负面词 - 金融领域
            "下降": -1.0, "降低": -1.0, "下滑": -1.0, "减少": -1.0, "下跌": -1.0, "下落": -1.0,
            "衰退": -1.0, "衰落": -1.0, "衰弱": -1.0, "萎缩": -1.0, "萎靡": -1.0, "萎缩": -1.0,
            "亏损": -1.0, "亏本": -1.0, "亏空": -1.0, "损失": -1.0, "损耗": -1.0, "损害": -1.0,
            "风险": -1.0, "危机": -1.0, "危险": -1.0, "威胁": -1.0, "挑战": -1.0, "困难": -1.0,
            "问题": -1.0, "障碍": -1.0, "阻碍": -1.0, "阻力": -1.0, "阻碍": -1.0, "阻挠": -1.0,
            "限制": -1.0, "限定": -1.0, "限度": -1.0, "限量": -1.0, "限额": -1.0, "限制": -1.0,
            "违规": -1.0, "违法": -1.0, "违约": -1.0, "违背": -1.0, "违反": -1.0, "违纪": -1.0,
            "失败": -1.0, "失误": -1.0, "失策": -1.0, "失利": -1.0, "失效": -1.0, "失控": -1.0,
            "利空": -1.0, "看空": -1.0, "看跌": -1.0, "看淡": -1.0, "看低": -1.0, "看弱": -1.0,
            "贬值": -1.0, "减值": -1.0, "降值": -1.0, "降级": -1.0, "减速": -1.0, "减质": -1.0,
            "减效": -1.0, "减收": -1.0, "减益": -1.0, "减盈": -1.0, "减利": -1.0, "减值": -1.0,
            "亏空": -1.0, "亏损扩大": -1.0, "亏损加剧": -1.0, "亏损严重": -1.0, "亏损持续": -1.0,
            "低增长": -1.0, "低回报": -1.0, "低收益": -1.0, "低利润": -1.0, "低价值": -1.0,
            "业绩下滑": -1.0, "业绩下降": -1.0, "业绩恶化": -1.0, "业绩不佳": -1.0, "业绩低迷": -1.0,
            "市场份额下降": -1.0, "市场占有率下降": -1.0, "市场地位下降": -1.0, "市场劣势": -1.0, "市场落后": -1.0,
            "竞争劣势": -1.0, "竞争力下降": -1.0, "核心竞争力不足": -1.0, "比较劣势": -1.0, "后发劣势": -1.0,
            "技术劣势": -1.0, "技术落后": -1.0, "技术停滞": -1.0, "技术瓶颈": -1.0, "技术障碍": -1.0
        }

        # 将常用金融情感词添加到情感词典中
        for word, score in financial_sentiment_dict.items():
            if word not in sentiment_dict:
                sentiment_dict[word] = score

        # 添加金融IT领域特定情感词
        financial_it_sentiment_dict = {
            # 正面词 - 金融IT领域
            "系统稳定": 1.0, "系统可靠": 1.0, "系统安全": 1.0, "系统高效": 1.0, "系统先进": 1.0,
            "平台稳定": 1.0, "平台可靠": 1.0, "平台安全": 1.0, "平台高效": 1.0, "平台先进": 1.0,
            "解决方案": 1.0, "整体解决方案": 1.0, "定制解决方案": 1.0, "综合解决方案": 1.0,
            "技术领先": 1.0, "技术创新": 1.0, "技术突破": 1.0, "技术优势": 1.0, "技术实力": 1.0,
            "产品创新": 1.0, "产品升级": 1.0, "产品优化": 1.0, "产品完善": 1.0, "产品丰富": 1.0,
            "服务优质": 1.0, "服务高效": 1.0, "服务完善": 1.0, "服务专业": 1.0, "服务创新": 1.0,
            "客户满意": 1.0, "客户认可": 1.0, "客户好评": 1.0, "客户信任": 1.0, "客户忠诚": 1.0,

            # 负面词 - 金融IT领域
            "系统不稳定": -1.0, "系统故障": -1.0, "系统漏洞": -1.0, "系统缺陷": -1.0, "系统风险": -1.0,
            "平台不稳定": -1.0, "平台故障": -1.0, "平台漏洞": -1.0, "平台缺陷": -1.0, "平台风险": -1.0,
            "解决方案不足": -1.0, "解决方案缺陷": -1.0, "解决方案落后": -1.0, "解决方案不完善": -1.0,
            "技术落后": -1.0, "技术停滞": -1.0, "技术瓶颈": -1.0, "技术劣势": -1.0, "技术不足": -1.0,
            "产品落后": -1.0, "产品缺陷": -1.0, "产品不足": -1.0, "产品单一": -1.0, "产品老化": -1.0,
            "服务不足": -1.0, "服务缺失": -1.0, "服务滞后": -1.0, "服务不专业": -1.0, "服务差": -1.0,
            "客户流失": -1.0, "客户投诉": -1.0, "客户不满": -1.0, "客户抱怨": -1.0, "客户流失": -1.0
        }

        # 将金融IT领域特定情感词添加到情感词典中
        for word, score in financial_it_sentiment_dict.items():
            if word not in sentiment_dict:
                sentiment_dict[word] = score

        # 统计词典信息
        positive_count = sum(1 for v in sentiment_dict.values() if v > 0)
        negative_count = sum(1 for v in sentiment_dict.values() if v < 0)
        print(f"成功加载情感词典，共 {len(sentiment_dict)} 个词")
        print(f"其中正面情感词 {positive_count} 个，负面情感词 {negative_count} 个")

        return sentiment_dict

    except Exception as e:
        print(f"加载情感词典时出错: {e}")
        # 如果加载失败，返回一个基本的情感词典
        basic_dict = {
            # 正面词 - 基本
            "利好": 1.0, "增长": 1.0, "上升": 1.0, "盈利": 1.0, "优势": 1.0, "成功": 1.0,
            "创新": 1.0, "领先": 1.0, "突破": 1.0, "提高": 1.0, "改善": 1.0, "机遇": 1.0,
            "发展": 1.0, "扩张": 1.0, "扩大": 1.0, "拓展": 1.0, "延伸": 1.0, "延展": 1.0,
            "推进": 1.0, "推动": 1.0, "促进": 1.0, "加速": 1.0, "加快": 1.0, "加强": 1.0,
            "支持": 1.0, "支撑": 1.0, "支援": 1.0, "帮助": 1.0, "协助": 1.0, "协同": 1.0,
            "合作": 1.0, "合力": 1.0, "合规": 1.0, "合理": 1.0, "合法": 1.0, "合适": 1.0,

            # 负面词 - 基本
            "风险": -1.0, "下降": -1.0, "亏损": -1.0, "挑战": -1.0, "受限": -1.0, "失败": -1.0,
            "问题": -1.0, "困难": -1.0, "危机": -1.0, "下滑": -1.0, "降低": -1.0, "威胁": -1.0,
            "违规": -1.0, "违法": -1.0, "违约": -1.0, "违背": -1.0, "违反": -1.0, "违纪": -1.0,
            "失败": -1.0, "失误": -1.0, "失策": -1.0, "失利": -1.0, "失效": -1.0, "失控": -1.0,
            "衰退": -1.0, "衰落": -1.0, "衰弱": -1.0, "萎缩": -1.0, "萎靡": -1.0, "萎缩": -1.0,
            "亏损": -1.0, "亏本": -1.0, "亏空": -1.0, "损失": -1.0, "损耗": -1.0, "损害": -1.0
        }
        print(f"使用基本情感词典，共 {len(basic_dict)} 个词")
        return basic_dict


def sentiment_analysis_by_dict(text, keywords, sentiment_dict):
    """
    基于情感词典对关键词进行情感分析
    增强版：支持部分匹配和多词匹配，优化金融研报文本的情感分析

    参数:
        text (str): 预处理后的文本
        keywords (list): 关键词列表，每个元素为(word, weight)元组
        sentiment_dict (dict): 情感词典

    返回:
        tuple: (overall_score, keyword_scores, matched_words) 整体情感得分、关键词情感得分和匹配到的情感词
    """
    try:
        print("开始基于情感词典进行情感分析...")

        # 检查输入
        if not text or len(text.strip()) == 0:
            print("错误: 输入文本为空")
            return 0, [], []

        if not keywords or len(keywords) == 0:
            print("错误: 关键词列表为空")
            return 0, [], []

        if not sentiment_dict or len(sentiment_dict) == 0:
            print("错误: 情感词典为空")
            return 0, [], []

        # 将文本分词，支持更灵活的匹配
        import jieba
        words = list(jieba.cut(text))
        print(f"文本分词完成，共 {len(words)} 个词")

        # 计算整体情感得分
        overall_score = 0
        matched_words = []

        print("正在进行情感词匹配...")

        # 1. 直接匹配 - 优先匹配完整词
        direct_matches = 0
        for word in words:
            if word in sentiment_dict:
                overall_score += sentiment_dict[word]
                matched_words.append((word, sentiment_dict[word]))
                direct_matches += 1

        print(f"直接匹配到 {direct_matches} 个情感词")

        # 2. 对于较长的词，尝试部分匹配 - 处理复合词和短语
        partial_matches = 0
        for dict_word in sentiment_dict:
            if len(dict_word) >= 2:
                # 只考虑较长的情感词
                if dict_word in text and dict_word not in [w[0] for w in matched_words]:
                    overall_score += sentiment_dict[dict_word]
                    matched_words.append((dict_word, sentiment_dict[dict_word]))
                    partial_matches += 1

        print(f"部分匹配到 {partial_matches} 个情感词")

        # 3. 对于短语，尝试窗口匹配 - 处理分散在不同位置的词组
        window_matches = 0
        # 使用不同的窗口大小，更灵活地匹配短语
        for window_size in [3, 4, 5]:
            if len(words) < window_size:
                continue

            for i in range(len(words) - window_size + 1):
                phrase = ''.join(words[i:i+window_size])
                for dict_word in sentiment_dict:
                    if len(dict_word) >= 3 and dict_word in phrase and dict_word not in [w[0] for w in matched_words]:
                        overall_score += sentiment_dict[dict_word]
                        matched_words.append((dict_word, sentiment_dict[dict_word]))
                        window_matches += 1

        print(f"窗口匹配到 {window_matches} 个情感词")

        # 4. 特殊处理金融IT领域的复合词
        compound_matches = 0
        financial_it_compounds = [
            ("系统", "稳定"), ("系统", "可靠"), ("系统", "安全"), ("系统", "高效"), ("系统", "先进"),
            ("平台", "稳定"), ("平台", "可靠"), ("平台", "安全"), ("平台", "高效"), ("平台", "先进"),
            ("解决", "方案"), ("整体", "解决", "方案"), ("定制", "解决", "方案"), ("综合", "解决", "方案"),
            ("技术", "领先"), ("技术", "创新"), ("技术", "突破"), ("技术", "优势"), ("技术", "实力"),
            ("产品", "创新"), ("产品", "升级"), ("产品", "优化"), ("产品", "完善"), ("产品", "丰富"),
            ("服务", "优质"), ("服务", "高效"), ("服务", "完善"), ("服务", "专业"), ("服务", "创新"),
            ("客户", "满意"), ("客户", "认可"), ("客户", "好评"), ("客户", "信任"), ("客户", "忠诚")
        ]

        # 在文本中查找这些复合词
        for compound in financial_it_compounds:
            # 将复合词组合成一个字符串
            compound_str = ''.join(compound)

            # 检查复合词是否在情感词典中
            if compound_str in sentiment_dict:
                # 检查文本中是否包含所有复合词的部分
                all_parts_found = True
                for part in compound:
                    if part not in text:
                        all_parts_found = False
                        break

                # 如果所有部分都找到了，且复合词尚未匹配
                if all_parts_found and compound_str not in [w[0] for w in matched_words]:
                    overall_score += sentiment_dict[compound_str]
                    matched_words.append((compound_str, sentiment_dict[compound_str]))
                    compound_matches += 1

        print(f"复合词匹配到 {compound_matches} 个情感词")

        # 如果没有匹配到任何情感词，使用关键词本身进行情感分析
        if not matched_words:
            print("未匹配到任何情感词，使用关键词本身进行情感分析...")

            # 使用关键词本身进行情感分析
            for keyword, weight in keywords:
                # 使用更全面的启发式规则判断关键词的情感倾向
                if keyword in ["证券", "金融", "服务", "研究", "财富", "科技", "系统", "平台", "解决方案",
                              "技术", "产品", "服务", "客户", "市场", "业务", "发展", "创新", "领先",
                              "优势", "能力", "实力", "专业", "高效", "稳定", "安全", "可靠"]:
                    matched_words.append((keyword, 1.0))
                    overall_score += 1.0
                elif keyword in ["风险", "问题", "挑战", "困难", "障碍", "威胁", "危机", "下降",
                                "下滑", "减少", "亏损", "损失", "缺陷", "漏洞", "故障", "失败",
                                "不足", "劣势", "落后", "停滞", "瓶颈"]:
                    matched_words.append((keyword, -1.0))
                    overall_score += -1.0

        # 如果仍然没有匹配到任何情感词，使用固定的中性情感得分
        if not matched_words:
            print("未匹配到任何情感词，使用固定的中性情感得分...")
            matched_words = [("金融研报", 0.0)]
            overall_score = 0.0

        # 归一化整体情感得分到[-1, 1]区间
        if matched_words:
            # 使用加权平均，考虑情感词的强度
            weighted_sum = sum(abs(score) * score for _, score in matched_words)
            weight_sum = sum(abs(score) for _, score in matched_words)
            if weight_sum > 0:
                overall_score = weighted_sum / weight_sum
            else:
                overall_score = 0

        print(f"整体情感得分: {overall_score:.4f}")

        # 计算关键词的情感得分
        keyword_scores = []
        print(f"正在分析 {len(keywords)} 个关键词的情感得分...")

        for keyword, weight in keywords:
            # 初始化得分
            score = 0
            matched_sentiment_words = []

            # 1. 直接匹配
            if keyword in sentiment_dict:
                score = sentiment_dict[keyword]
                matched_sentiment_words.append((keyword, score))

            # 2. 部分匹配 - 更全面的匹配策略
            if score == 0:
                # 2.1 关键词包含情感词
                for dict_word in sentiment_dict:
                    if len(dict_word) >= 2 and dict_word in keyword:
                        matched_sentiment_words.append((dict_word, sentiment_dict[dict_word]))

                # 2.2 情感词包含关键词
                for dict_word in sentiment_dict:
                    if len(keyword) >= 2 and keyword in dict_word:
                        matched_sentiment_words.append((dict_word, sentiment_dict[dict_word]))

                # 2.3 关键词和情感词有重叠部分（至少2个字符）
                for dict_word in sentiment_dict:
                    if len(dict_word) >= 3 and len(keyword) >= 3:
                        # 检查是否有重叠部分
                        for i in range(len(keyword) - 1):
                            if keyword[i:i+2] in dict_word:
                                matched_sentiment_words.append((dict_word, sentiment_dict[dict_word] * 0.8))  # 降低权重
                                break

                # 如果有匹配的情感词，计算加权平均得分
                if matched_sentiment_words:
                    total_score = sum(s for _, s in matched_sentiment_words)
                    score = total_score / len(matched_sentiment_words)

            # 3. 如果仍然没有匹配到情感词，使用启发式规则
            if score == 0:
                # 3.1 金融IT领域正面词汇
                if keyword in ["证券", "金融", "服务", "研究", "财富", "科技", "系统", "平台", "解决方案",
                              "技术", "产品", "服务", "客户", "市场", "业务", "发展", "创新", "领先",
                              "优势", "能力", "实力", "专业", "高效", "稳定", "安全", "可靠"]:
                    score = 0.8
                # 3.2 金融IT领域负面词汇
                elif keyword in ["风险", "问题", "挑战", "困难", "障碍", "威胁", "危机", "下降",
                                "下滑", "减少", "亏损", "损失", "缺陷", "漏洞", "故障", "失败",
                                "不足", "劣势", "落后", "停滞", "瓶颈"]:
                    score = -0.8
                # 3.3 默认为轻微正面
                else:
                    score = 0.2

            # 添加到结果中
            keyword_scores.append((keyword, score, weight))

        print(f"情感词典分析完成：匹配到 {len(matched_words)} 个情感词")
        if len(matched_words) > 0:
            print(f"前5个匹配词: {matched_words[:min(5, len(matched_words))]}")

        return overall_score, keyword_scores, matched_words

    except Exception as e:
        print(f"基于情感词典进行情感分析时出错: {e}")
        return 0, [], []


def sentiment_analysis_by_finbert(text, keywords, max_length=512):
    """
    使用FinBERT模型对文本和关键词进行情感分析，增强稳定性和一致性
    优化处理中文金融研报，使用本地下载好的FinBERT模型

    参数:
        text (str): 预处理后的文本
        keywords (list): 关键词列表，每个元素为(word, weight)元组
        max_length (int): 输入文本的最大长度

    返回:
        tuple: (overall_score, keyword_scores) 整体情感得分和关键词情感得分
    """
    # 设置随机种子，确保结果的一致性
    import random
    import os
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(42)

    # 设置本地FinBERT模型路径
    finbert_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'finbert')

    try:
        print("开始使用FinBERT进行情感分析...")

        # 检查输入
        if not text or len(text.strip()) == 0:
            print("错误: 输入文本为空")
            return 0, []

        if not keywords or len(keywords) == 0:
            print("错误: 关键词列表为空")
            return 0, []

        # 加载本地FinBERT模型
        print("正在加载本地FinBERT模型...")
        print(f"模型路径: {finbert_dir}")

        # 检查模型文件是否存在
        if not os.path.exists(finbert_dir):
            print(f"错误: 本地FinBERT模型目录不存在: {finbert_dir}")
            print("使用备选情感分析方法...")
            return sentiment_analysis_by_snownlp(text, keywords)

        if not os.path.exists(os.path.join(finbert_dir, 'pytorch_model.bin')):
            print(f"错误: 本地FinBERT模型文件不存在: {os.path.join(finbert_dir, 'pytorch_model.bin')}")
            print("使用备选情感分析方法...")
            return sentiment_analysis_by_snownlp(text, keywords)

        try:
            # 加载分词器和模型
            print("正在加载FinBERT分词器和模型...")
            tokenizer = AutoTokenizer.from_pretrained(finbert_dir)
            model = AutoModelForSequenceClassification.from_pretrained(finbert_dir)

            # 确保模型在评估模式
            model.eval()

            # 将模型移动到GPU（如果可用）
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = model.to(device)

            print(f"本地FinBERT模型加载成功，使用设备: {device}")

        except Exception as model_error:
            print(f"加载本地FinBERT模型时出错: {model_error}")
            print("使用备选情感分析方法...")
            return sentiment_analysis_by_snownlp(text, keywords)

        # 预处理文本，确保质量
        print("正在预处理文本...")
        text = preprocess_for_sentiment(text)
        print(f"预处理后文本长度: {len(text)} 字符")

        # 对整体文本进行情感分析
        # 将文本分割成较小的块，以适应模型的最大输入长度，并确保在句子边界处分割
        print("正在将文本分割成块...")
        chunks = split_text_into_segments(text, max_length=max_length)
        print(f"文本被分割成 {len(chunks)} 个块")

        overall_scores = []
        chunk_weights = []
        chunk_probabilities = []  # 存储每个块的概率分布

        print(f"使用FinBERT分析文本块...")
        for i, chunk in enumerate(chunks):
            if not chunk.strip():
                continue

            if i % 5 == 0 or i == len(chunks) - 1:
                print(f"  分析第 {i+1}/{len(chunks)} 个块...")

            # 对中文文本进行特殊处理，确保分词正确
            inputs = tokenizer(chunk, return_tensors='pt', truncation=True, max_length=max_length, padding=True)

            # 将输入移动到相同的设备
            inputs = {k: v.to(device) for k, v in inputs.items()}

            with torch.no_grad():
                outputs = model(**inputs)

            logits = outputs.logits
            probabilities = torch.softmax(logits, dim=1)

            # 保存概率分布
            probs = probabilities[0].cpu().numpy()
            chunk_probabilities.append(probs)

            # FinBERT输出三个类别：负面(0)、中性(1)、正面(2)
            # 计算情感得分：正面概率 - 负面概率
            score = probabilities[0][2].item() - probabilities[0][0].item()

            # 使用块的长度作为权重，较长的块权重更高
            weight = len(chunk)
            overall_scores.append(score)
            chunk_weights.append(weight)

        # 计算加权平均的整体情感得分
        if overall_scores:
            overall_score = sum(s * w for s, w in zip(overall_scores, chunk_weights)) / sum(chunk_weights)

            # 计算整体概率分布
            avg_probs = np.average(chunk_probabilities, axis=0, weights=chunk_weights)
            neg_prob, neu_prob, pos_prob = avg_probs

            print(f"整体情感概率分布: 负面={neg_prob:.4f}, 中性={neu_prob:.4f}, 正面={pos_prob:.4f}")
        else:
            overall_score = 0
            print("警告: 没有有效的文本块进行分析")

        print(f"整体情感得分: {overall_score:.4f}")

        # 确定整体情感倾向
        if overall_score > 0.2:
            sentiment = "正面"
        elif overall_score < -0.2:
            sentiment = "负面"
        else:
            sentiment = "中性"
        print(f"整体情感倾向: {sentiment}")

        # 对每个关键词进行情感分析，使用上下文
        keyword_scores = []

        print(f"使用FinBERT分析关键词，共 {len(keywords)} 个关键词...")
        for i, (keyword, weight) in enumerate(keywords):
            if i % 10 == 0:
                print(f"  分析第 {i+1}-{min(i+10, len(keywords))}/{len(keywords)} 个关键词...")

            # 查找包含关键词的上下文
            contexts = find_multiple_contexts_for_keyword(text, keyword, max_contexts=3, context_size=150)

            if contexts:
                # 对每个上下文进行情感分析
                context_scores = []
                for context in contexts:
                    # 确保上下文长度适合模型
                    if len(context) > max_length:
                        context = context[:max_length]

                    inputs = tokenizer(context, return_tensors='pt', truncation=True, max_length=max_length, padding=True)
                    inputs = {k: v.to(device) for k, v in inputs.items()}

                    with torch.no_grad():
                        outputs = model(**inputs)

                    logits = outputs.logits
                    probabilities = torch.softmax(logits, dim=1)

                    # 计算上下文的情感得分
                    context_score = probabilities[0][2].item() - probabilities[0][0].item()
                    context_scores.append(context_score)

                # 取加权平均作为关键词的情感得分
                # 权重基于上下文长度和关键词在上下文中的位置
                context_weights = []
                for context in contexts:
                    # 关键词在上下文中的位置（越靠近中间权重越高）
                    pos = context.find(keyword)
                    if pos == -1:  # 如果找不到关键词（不应该发生）
                        pos = len(context) // 2

                    # 计算位置权重：关键词越靠近上下文中间，权重越高
                    center_pos = len(context) / 2
                    position_weight = 1 - abs(pos - center_pos) / len(context)

                    # 上下文长度权重
                    length_weight = min(1.0, len(context) / 100)

                    # 综合权重
                    weight = position_weight * 0.7 + length_weight * 0.3
                    context_weights.append(weight)

                # 计算加权平均得分
                score = sum(s * w for s, w in zip(context_scores, context_weights)) / sum(context_weights)
            else:
                # 如果找不到上下文，直接分析关键词本身
                inputs = tokenizer(keyword, return_tensors='pt', padding=True)
                inputs = {k: v.to(device) for k, v in inputs.items()}

                with torch.no_grad():
                    outputs = model(**inputs)

                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=1)

                # 计算关键词的情感得分
                score = probabilities[0][2].item() - probabilities[0][0].item()

                # 对于单独的关键词，降低得分的绝对值（因为缺乏上下文）
                score = score * 0.8

            # 将关键词得分添加到结果中
            keyword_scores.append((keyword, score, weight))

        print("FinBERT情感分析完成")
        return overall_score, keyword_scores

    except Exception as e:
        print(f"使用FinBERT进行情感分析时出错: {e}")
        print("使用备选情感分析方法...")
        return sentiment_analysis_by_snownlp(text, keywords)


def preprocess_for_sentiment(text):
    """
    预处理文本以提高情感分析的准确性

    参数:
        text (str): 原始文本

    返回:
        str: 预处理后的文本
    """
    import re

    # 清理特殊字符
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)

    # 规范化空白字符
    text = re.sub(r'\s+', ' ', text).strip()

    # 移除URL
    text = re.sub(r'https?://\S+|www\.\S+', '', text)

    # 移除HTML标签
    text = re.sub(r'<.*?>', '', text)

    # 移除过多的标点符号
    text = re.sub(r'([。，！？；：、""''（）【】《》])\1+', r'\1', text)

    return text


def split_text_into_segments(text, max_length=500, overlap=100):
    """
    将长文本分割成重叠的段落，以便进行情感分析

    参数:
        text (str): 原始文本
        max_length (int): 每个段落的最大长度
        overlap (int): 段落之间的重叠长度

    返回:
        list: 分割后的段落列表
    """
    if len(text) <= max_length:
        return [text]

    segments = []
    start = 0

    while start < len(text):
        end = min(start + max_length, len(text))

        # 如果不是最后一个段落，尝试在句子边界处分割
        if end < len(text):
            # 查找最后一个句子结束符
            sentence_end = max(
                text.rfind('。', start, end),
                text.rfind('！', start, end),
                text.rfind('？', start, end),
                text.rfind('\n', start, end)
            )

            if sentence_end > start + max_length // 2:
                end = sentence_end + 1

        segments.append(text[start:end])
        start = end - overlap if end < len(text) else len(text)

    return segments


def find_multiple_contexts_for_keyword(text, keyword, max_contexts=3, context_size=100):
    """
    查找包含关键词的多个上下文

    参数:
        text (str): 原始文本
        keyword (str): 关键词
        max_contexts (int): 最大上下文数量
        context_size (int): 上下文大小（关键词前后的字符数）

    返回:
        list: 上下文列表
    """
    contexts = []
    start_pos = 0

    for _ in range(max_contexts):
        pos = text.find(keyword, start_pos)
        if pos == -1:
            break

        start = max(0, pos - context_size)
        end = min(len(text), pos + len(keyword) + context_size)

        # 尝试在句子边界处扩展上下文
        if start > 0:
            sentence_start = max(
                text.rfind('。', 0, start),
                text.rfind('！', 0, start),
                text.rfind('？', 0, start),
                text.rfind('\n', 0, start)
            )
            if sentence_start != -1:
                start = sentence_start + 1

        if end < len(text):
            sentence_end = min(
                text.find('。', end) if text.find('。', end) != -1 else len(text),
                text.find('！', end) if text.find('！', end) != -1 else len(text),
                text.find('？', end) if text.find('？', end) != -1 else len(text),
                text.find('\n', end) if text.find('\n', end) != -1 else len(text)
            )
            if sentence_end != -1:
                end = sentence_end + 1

        contexts.append(text[start:end])
        start_pos = pos + len(keyword)

    return contexts


def sentiment_analysis_by_snownlp(text, keywords):
    """
    使用SnowNLP进行情感分析（FinBERT的备选方案）

    参数:
        text (str): 预处理后的文本
        keywords (list): 关键词列表，每个元素为(word, weight)元组

    返回:
        tuple: (overall_score, keyword_scores) 整体情感得分和关键词情感得分
    """
    try:
        from snownlp import SnowNLP

        # 对整体文本进行情感分析
        try:
            s = SnowNLP(text)
            # SnowNLP的情感分析结果是[0, 1]之间的值，0表示负面，1表示正面
            # 将其转换为[-1, 1]之间的值
            overall_score = 2 * s.sentiments - 1
        except Exception as text_error:
            print(f"使用SnowNLP分析整体文本时出错: {text_error}")
            overall_score = 0

        # 对每个关键词进行情感分析
        keyword_scores = []

        for keyword, weight in keywords:
            try:
                s = SnowNLP(keyword)
                # 将情感得分转换为[-1, 1]之间的值
                score = 2 * s.sentiments - 1
            except Exception as keyword_error:
                print(f"使用SnowNLP分析关键词'{keyword}'时出错: {keyword_error}")
                score = 0

            keyword_scores.append((keyword, score, weight))

        print("使用SnowNLP完成情感分析")
        return overall_score, keyword_scores

    except ImportError:
        print("SnowNLP库未安装，使用简单规则进行情感分析...")
        return sentiment_analysis_by_simple_rules(text, keywords)
    except Exception as e:
        print(f"使用SnowNLP进行情感分析时出错: {e}")
        return sentiment_analysis_by_simple_rules(text, keywords)


def sentiment_analysis_by_simple_rules(text, keywords):
    """
    使用简单规则进行情感分析（最后的备选方案）

    参数:
        text (str): 预处理后的文本
        keywords (list): 关键词列表，每个元素为(word, weight)元组

    返回:
        tuple: (overall_score, keyword_scores) 整体情感得分和关键词情感得分
    """
    # 简单的情感词典
    simple_sentiment_dict = {
        "利好": 1.0, "增长": 0.8, "上升": 0.7, "盈利": 0.9, "优势": 0.6, "成功": 0.9,
        "创新": 0.7, "领先": 0.8, "突破": 0.7, "提高": 0.6, "改善": 0.5, "机遇": 0.6,
        "风险": -0.8, "下降": -0.7, "亏损": -0.9, "挑战": -0.5, "受限": -0.6, "失败": -0.9,
        "问题": -0.7, "困难": -0.6, "危机": -0.8, "下滑": -0.7, "降低": -0.6, "威胁": -0.7
    }

    # 计算整体情感得分
    words = text.split()
    sentiment_scores = []

    for word in words:
        if word in simple_sentiment_dict:
            sentiment_scores.append(simple_sentiment_dict[word])

    overall_score = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0

    # 计算关键词情感得分
    keyword_scores = []

    for keyword, weight in keywords:
        score = 0
        if keyword in simple_sentiment_dict:
            score = simple_sentiment_dict[keyword]
        else:
            # 如果关键词不在情感词典中，尝试分解关键词并计算得分
            keyword_words = keyword.split()
            keyword_scores_list = []
            for word in keyword_words:
                if word in simple_sentiment_dict:
                    keyword_scores_list.append(simple_sentiment_dict[word])

            if keyword_scores_list:
                score = sum(keyword_scores_list) / len(keyword_scores_list)

        keyword_scores.append((keyword, score, weight))

    print("使用简单规则完成情感分析")
    return overall_score, keyword_scores


def compare_sentiment_results(dict_score, finbert_score, dict_keywords, finbert_keywords):
    """
    比较两种情感分析方法的结果，提供详细的对比分析

    参数:
        dict_score (float): 基于情感词典的整体情感得分
        finbert_score (float): 基于FinBERT的整体情感得分
        dict_keywords (list): 基于情感词典的关键词情感得分
        finbert_keywords (list): 基于FinBERT的关键词情感得分

    返回:
        tuple: (comparison_df, agreement_rate, combined_score, sentiment_summary) 比较结果DataFrame、一致率、综合得分和情感摘要
    """
    try:
        print("开始比较两种情感分析方法的结果...")

        # 检查输入
        if dict_keywords is None or finbert_keywords is None:
            print("错误: 关键词得分列表为空")
            return pd.DataFrame(), 0, 0, {}

        # 创建比较结果DataFrame
        comparison_data = []

        # 合并两种方法的关键词
        all_keywords = set([k[0] for k in dict_keywords] + [k[0] for k in finbert_keywords])
        print(f"两种方法共有 {len(all_keywords)} 个关键词")

        # 使用更精细的情感分类
        def get_sentiment_category(score):
            if score > 0.5:
                return "强正面"
            elif score > 0.2:
                return "正面"
            elif score > -0.2:
                return "中性"
            elif score > -0.5:
                return "负面"
            else:
                return "强负面"

        # 整体情感分析
        dict_sentiment = get_sentiment_category(dict_score)
        finbert_sentiment = get_sentiment_category(finbert_score)

        # 判断整体情感倾向一致性
        if dict_sentiment == finbert_sentiment:
            overall_consistency = "完全一致"
        elif (dict_sentiment.endswith("正面") and finbert_sentiment.endswith("正面")) or \
             (dict_sentiment.endswith("负面") and finbert_sentiment.endswith("负面")):
            overall_consistency = "基本一致"
        elif dict_sentiment == "中性" or finbert_sentiment == "中性":
            overall_consistency = "部分一致"
        else:
            overall_consistency = "不一致"

        print(f"整体情感分析比较: 词典法={dict_sentiment}({dict_score:.4f}), FinBERT={finbert_sentiment}({finbert_score:.4f}), 一致性={overall_consistency}")

        # 关键词情感分析
        agreement_levels = {"完全一致": 0, "基本一致": 0, "部分一致": 0, "不一致": 0}

        for keyword in all_keywords:
            # 查找词典方法的得分
            dict_score_k = 0
            dict_weight = 0
            for k, s, w in dict_keywords:
                if k == keyword:
                    dict_score_k = s
                    dict_weight = w
                    break

            # 查找FinBERT方法的得分
            finbert_score_k = 0
            finbert_weight = 0
            for k, s, w in finbert_keywords:
                if k == keyword:
                    finbert_score_k = s
                    finbert_weight = w
                    break

            # 判断两种方法的情感倾向
            dict_sentiment_k = get_sentiment_category(dict_score_k)
            finbert_sentiment_k = get_sentiment_category(finbert_score_k)

            # 判断一致性级别
            if dict_sentiment_k == finbert_sentiment_k:
                consistency = "完全一致"
            elif (dict_sentiment_k.endswith("正面") and finbert_sentiment_k.endswith("正面")) or \
                 (dict_sentiment_k.endswith("负面") and finbert_sentiment_k.endswith("负面")):
                consistency = "基本一致"
            elif dict_sentiment_k == "中性" or finbert_sentiment_k == "中性":
                consistency = "部分一致"
            else:
                consistency = "不一致"

            # 更新一致性统计
            agreement_levels[consistency] += 1

            # 计算得分差异
            score_diff = abs(dict_score_k - finbert_score_k)

            # 计算综合得分（两种方法的加权平均）
            # 根据两种方法的特点，给予不同的权重
            if abs(dict_score_k) > 0.5:  # 情感词典给出强烈情感
                dict_weight_factor = 0.6
                finbert_weight_factor = 0.4
            elif abs(finbert_score_k) > 0.5:  # FinBERT给出强烈情感
                dict_weight_factor = 0.4
                finbert_weight_factor = 0.6
            else:  # 两种方法都不确定
                dict_weight_factor = 0.5
                finbert_weight_factor = 0.5

            combined_score_k = dict_score_k * dict_weight_factor + finbert_score_k * finbert_weight_factor
            combined_sentiment_k = get_sentiment_category(combined_score_k)

            # 添加到比较数据中
            comparison_data.append({
                "关键词": keyword,
                "词典情感得分": round(dict_score_k, 4),
                "词典情感倾向": dict_sentiment_k,
                "FinBERT情感得分": round(finbert_score_k, 4),
                "FinBERT情感倾向": finbert_sentiment_k,
                "一致性": consistency,
                "得分差异": round(score_diff, 4),
                "综合得分": round(combined_score_k, 4),
                "综合情感倾向": combined_sentiment_k
            })

        # 创建DataFrame并按得分差异排序
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values(by="得分差异", ascending=False)

        # 计算一致率统计
        total_keywords = len(comparison_df)
        agreement_stats = {}

        if total_keywords > 0:
            for level, count in agreement_levels.items():
                agreement_stats[level] = count / total_keywords

            print(f"关键词情感分析一致性: 完全一致={agreement_stats['完全一致']:.2f}, " +
                  f"基本一致={agreement_stats['基本一致']:.2f}, " +
                  f"部分一致={agreement_stats['部分一致']:.2f}, " +
                  f"不一致={agreement_stats['不一致']:.2f}")
        else:
            print("警告: 没有关键词可以比较")

        # 计算综合一致率（完全一致和基本一致的比例）
        agreement_rate = agreement_stats.get("完全一致", 0) + agreement_stats.get("基本一致", 0)

        # 计算综合情感得分
        # 根据两种方法的特点，给予不同的权重
        if abs(dict_score) > 0.5:  # 情感词典给出强烈情感
            dict_weight_factor = 0.6
            finbert_weight_factor = 0.4
        elif abs(finbert_score) > 0.5:  # FinBERT给出强烈情感
            dict_weight_factor = 0.4
            finbert_weight_factor = 0.6
        else:  # 两种方法都不确定
            dict_weight_factor = 0.5
            finbert_weight_factor = 0.5

        combined_score = dict_score * dict_weight_factor + finbert_score * finbert_weight_factor
        combined_sentiment = get_sentiment_category(combined_score)

        print(f"综合情感分析结果: {combined_sentiment}({combined_score:.4f})")

        # 创建情感分析摘要
        sentiment_summary = {
            "整体情感分析": {
                "词典方法": {"得分": dict_score, "情感倾向": dict_sentiment},
                "FinBERT方法": {"得分": finbert_score, "情感倾向": finbert_sentiment},
                "一致性": overall_consistency,
                "综合结果": {"得分": combined_score, "情感倾向": combined_sentiment}
            },
            "关键词情感分析": {
                "一致性统计": agreement_stats,
                "综合一致率": agreement_rate
            },
            "情感分布": {
                "正面关键词比例": len(comparison_df[comparison_df["综合情感倾向"].str.contains("正面")]) / total_keywords if total_keywords > 0 else 0,
                "负面关键词比例": len(comparison_df[comparison_df["综合情感倾向"].str.contains("负面")]) / total_keywords if total_keywords > 0 else 0,
                "中性关键词比例": len(comparison_df[comparison_df["综合情感倾向"] == "中性"]) / total_keywords if total_keywords > 0 else 0
            }
        }

        return comparison_df, agreement_rate, combined_score, sentiment_summary

    except Exception as e:
        print(f"比较情感分析结果时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame(), 0, 0, {}
