{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["这篇文章是公众号关注者郝童鞋今早发给我的，在此谢谢郝童鞋。\n", "\n", "文章基于简单算法和人工判断，使用多阶段剔除法，构建了``中文金融情感词典CFSD（Chinese Financial Sentiment Dictionary）``， 这个词典能帮到那些想用文本分析研究会计金融领域的中文文档的研究者。 CFSD词典有1489个负面词，1108个正面词。并且简单讨论了CFSD词典的应用领域。\n", "\n", "本篇教程主要分为两部分：\n", "\n", "1. 这篇论文如何构建``中文金融情感词典`` \n", "2. 大邓将论文附录中的词典整理好给大家用\n", "\n", "### 一、构建中文金融情感词典 \n", "情感分析目前有两大方式，情感词典法和机器学习法。基于情感词典的文本分析，必须要有好用的词典。但由于语言差异，英文的情感词典无法直接应用于中文的情感分析，而且目前中文的情感词典（如HOWNET、DLUTSD、NTUSD）都是通用性词典（大多是形容词副词），并不是专业领域词典。Loughran和 McDonald (2011)曾经指出研究商业领域问题的文本数据不应该使用非商业领域数据集构建出的词典。\n", "\n", "因此本文作者使用HOWNET、DLUTSD、NTUSD三种词典作为初始词典，并搜集了在线路演纪要（online roadshow transcripts）、业绩说明电话会议纪要（earnings conference call transcripts）、IPO招股报告（IPO prospectus）及公司年报构建了基础语料库。基于算法和人工判断， 使用多阶段剔除法来构建 ``中文金融情感词典CFSD``。具体步骤：\n", "\n", "1. 合并HOWNET、DLUTSD、NTUSD三个情感词典，去除重复词\n", "2. 收集了1411篇在线路演纪要、7138篇业绩说明电话会议纪要、2043IPO招股报告和29737公司年报。jieba被用于分割文档，构建 ``基础语料``\n", "3. 计算步骤1所有的词在 ``基础语料`` 中的词频，词频数为0的词语不予考虑，剔除掉。与金融不相关的词语也剔除掉，最终构建了``CFSD0.0``版本中文金融情感词典。\n", "4. 所有的CFSD0.0版本的词语都来自与三个通用情感词典（HOWNET、DLUTSD、NTUSD），但这三个词典并不包含金融领域常出现的正面词和负面词。我们人工向``CFSD0.0``版情感词典加入了金融领域最常用的100个正面词100个负面词，构建出``CFSD0.1``版中文金融情感词典.\n", "5. Gensim是python中的一个文本分析库，在本步骤主要用来通过大量的语料训练处词向量。词向量可以使用余弦cos计算出相似性。在本步骤，计算出CFSD0.1版中每个词的词向量，进而从 ``基础语料`` 中发现每个词（CFSD0.1中的词）最相似的50个词。剔除掉与金融不关的词（包括相似词、同义词），构建出 ``CFSD0.2版的中文金融情感词典``\n", "6. 合并 ``CFSD0.0、CFSD0.1、 CFSD0.2 ``,剔除掉重复词，最终构建出 ``CFSD中文金融情感词典``\n", "\n", "构件好的CFSD词典有1489个负面词，1108个正面词。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 二、词典整理到csv文件中\n", "论文后面富有CFSD情感词典，如下\n", "## img/正面.png)\n", "我想先将这些内容全部复制到txt中，正面词表复制粘贴到 ``正面词典.txt``， 负面词表复制粘贴到 ``负面词典.txt``。\n", "## img/正面s.gif)\n", "通过中文正则表达式 ``[\\u4e00-\\u9fa5]+`` 把txt文件里面所有的中文词抽取出来，存到csv文件中。开始~"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import jieba\n", "import re\n", "import csv\n", "\n", "def extract_keywords(infile, outfile, header):\n", "    raw_kws_string = open(infile).read()\n", "    kws = re.findall('[\\u4e00-\\u9fa5]+', raw_kws_string)\n", "    csvf = open(outfile, 'w', encoding='gbk', newline='')\n", "    writer = csv.writer(csvf)\n", "    writer.writerow((header,))\n", "    for kw in set(kws):\n", "        writer.writerow((kw,))\n", "    csvf.close()\n", "\n", "extract_keywords(infile = '正面词典.txt', \n", "                 outfile = '正面词典.csv', \n", "                 header = 'postive')\n", "\n", "extract_keywords(infile = '负面词典.txt', \n", "                 outfile = '负面词典.csv', \n", "                 header = 'negative')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们的项目文件夹中出现了 ``正面词典.csv 、 负面词典.csv`` , 现在我们可以试着读取一下  正面词典.csv"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["1109"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "#正面词典.csv中有1109个词\n", "df = pd.read_csv('正面词典.csv', encoding='gbk')\n", "len(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["负面词典.csv中有1488个词"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["1488"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 = pd.read_csv('负面词典.csv', encoding='gbk')\n", "len(df2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["词典中的词数与论文中的 1108和 1489有出入，应该是复制粘贴时不够自信。下面我们看看词典中的内容"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>postive</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>优秀</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>改观</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>精美</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>真才实学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>今非昔比</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  postive\n", "0      优秀\n", "1      改观\n", "2      精美\n", "3    真才实学\n", "4    今非昔比"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["#正面词典前5\n", "df.head(5)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>negative</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>闭门造车</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>闭塞</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>云里雾里</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>拖累</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>过热</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  negative\n", "0     闭门造车\n", "1       闭塞\n", "2     云里雾里\n", "3       拖累\n", "4       过热"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["#负面词典前5\n", "df2.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}