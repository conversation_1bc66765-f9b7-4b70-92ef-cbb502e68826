{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 完整版\n", "\n", "本notebook整合了研报情感分析的完整流程，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 情感分析（词典法 + FinBERT）\n", "5. 结果可视化\n", "\n", "所有结果都在notebook内展示，无需外部文件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "try:\n", "    import camelot\n", "    CAMELOT_AVAILABLE = True\n", "except ImportError:\n", "    CAMELOT_AVAILABLE = False\n", "    print(\"警告: camelot-py不可用，高级表格提取功能将不可用\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 深度学习\n", "try:\n", "    import torch\n", "    from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "    TRANSFORMERS_AVAILABLE = True\n", "except ImportError:\n", "    TRANSFORMERS_AVAILABLE = False\n", "    print(\"警告: transformers不可用，FinBERT功能将不可用\")\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib使用系统默认字体，避免中文显示问题\n", "plt.rcParams['font.family'] = ['sans-serif']\n", "plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ 所有必要的库已导入完成\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"Camelot可用: {CAMELOT_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"Transformers可用: {TRANSFORMERS_AVAILABLE}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PDF文本提取模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:\n", "    \"\"\"\n", "    使用PyMuPDF提取PDF文本\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 按页分割的文本列表)\n", "    \"\"\"\n", "    if not PYMUPDF_AVAILABLE:\n", "        return None, []\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        all_text = \"\"\n", "        page_texts = []\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc[page_num]\n", "            text = page.get_text()\n", "            \n", "            if text.strip():\n", "                all_text += text + \"\\n\"\n", "                page_texts.append(text)\n", "        \n", "        doc.close()\n", "        return all_text, page_texts\n", "        \n", "    except Exception as e:\n", "        print(f\"PyMuPDF提取失败: {e}\")\n", "        return None, []\n", "\n", "def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    使用pdfplumber提取PDF文本和表格\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 表格列表)\n", "    \"\"\"\n", "    try:\n", "        all_text = \"\"\n", "        all_tables = []\n", "        \n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            for page_num, page in enumerate(pdf.pages):\n", "                # 提取文本\n", "                text = page.extract_text()\n", "                if text:\n", "                    all_text += text + \"\\n\"\n", "                \n", "                # 提取表格\n", "                tables = page.extract_tables()\n", "                for table in tables:\n", "                    if table and len(table) > 1:\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            all_tables.append(df)\n", "                        except Exception as e:\n", "                            print(f\"表格处理失败: {e}\")\n", "        \n", "        return all_text, all_tables\n", "        \n", "    except Exception as e:\n", "        print(f\"pdfplumber提取失败: {e}\")\n", "        return \"\", []\n", "\n", "def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    从PDF文件中提取文本和表格，使用多种方法确保提取完整性\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (提取的文本, 表格列表)\n", "    \"\"\"\n", "    print(f\"📄 开始提取PDF文件: {os.path.basename(pdf_path)}\")\n", "    \n", "    # 方法1: PyMuPDF提取文本\n", "    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)\n", "    \n", "    # 方法2: pdfplumber提取文本和表格\n", "    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)\n", "    \n", "    # 选择最佳文本提取结果\n", "    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):\n", "        best_text = pymupdf_text\n", "        print(f\"✅ 使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)\")\n", "    else:\n", "        best_text = pdfplumber_text\n", "        print(f\"✅ 使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)\")\n", "    \n", "    print(f\"📊 提取到 {len(tables)} 个表格\")\n", "    \n", "    return best_text, tables\n", "\n", "print(\"✅ PDF文本提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 文本预处理模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_stopwords(stopwords_path: str) -> set:\n", "    \"\"\"\n", "    加载停用词\n", "    \n", "    参数:\n", "        stopwords_path: 停用词文件路径\n", "    \n", "    返回:\n", "        停用词集合\n", "    \"\"\"\n", "    stopwords = set()\n", "    \n", "    # 默认停用词\n", "    default_stopwords = {\n", "        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',\n", "        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'\n", "    }\n", "    stopwords.update(default_stopwords)\n", "    \n", "    # 从文件加载停用词\n", "    if os.path.exists(stopwords_path):\n", "        try:\n", "            with open(stopwords_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    word = line.strip()\n", "                    if word:\n", "                        stopwords.add(word)\n", "            print(f\"✅ 从文件加载了 {len(stopwords)} 个停用词\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载停用词文件失败: {e}，使用默认停用词\")\n", "    else:\n", "        print(f\"⚠️ 停用词文件不存在: {stopwords_path}，使用默认停用词\")\n", "    \n", "    return stopwords\n", "\n", "def clean_text(text: str) -> str:\n", "    \"\"\"\n", "    清洗文本，去除特殊字符等\n", "    \n", "    参数:\n", "        text: 待清洗的文本\n", "    \n", "    返回:\n", "        清洗后的文本\n", "    \"\"\"\n", "    # 去除URL\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', '', text)\n", "    \n", "    # 去除HTML标签\n", "    text = re.sub(r'<.*?>', '', text)\n", "    \n", "    # 去除邮箱\n", "    text = re.sub(r'\\S*@\\S*\\s?', '', text)\n", "    \n", "    # 保留中文、英文、数字和基本标点\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9.,，。、；：''\"\"（）()？?!！\\s]+', ' ', text)\n", "    \n", "    # 去除多余的空白字符\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:\n", "    \"\"\"\n", "    文本预处理：分词、去停用词、过滤\n", "    \n", "    参数:\n", "        text: 待处理的文本\n", "        stopwords_path: 停用词文件路径\n", "        min_word_len: 最小词长度\n", "    \n", "    返回:\n", "        (过滤后的词列表, 过滤后的文本)\n", "    \"\"\"\n", "    print(\"🔄 开始文本预处理...\")\n", "    \n", "    if not text or len(text.strip()) == 0:\n", "        print(\"❌ 输入文本为空\")\n", "        return [], \"\"\n", "    \n", "    # 清洗文本\n", "    cleaned_text = clean_text(text)\n", "    print(f\"📝 文本清洗完成，长度: {len(cleaned_text)} 字符\")\n", "    \n", "    # 加载停用词\n", "    stopwords = load_stopwords(stopwords_path)\n", "    \n", "    # 分词\n", "    print(\"✂️ 开始分词...\")\n", "    words = jieba.lcut(cleaned_text)\n", "    print(f\"📊 分词完成，共 {len(words)} 个词\")\n", "    \n", "    # 过滤词语\n", "    filtered_words = []\n", "    for word in words:\n", "        word = word.strip()\n", "        if (len(word) >= min_word_len and \n", "            word not in stopwords and \n", "            not word.isdigit() and \n", "            not re.match(r'^[\\W_]+$', word)):\n", "            filtered_words.append(word)\n", "    \n", "    # 重新组合文本\n", "    filtered_text = ' '.join(filtered_words)\n", "    \n", "    print(f\"✅ 文本预处理完成，过滤后共 {len(filtered_words)} 个有效词\")\n", "    \n", "    return filtered_words, filtered_text\n", "\n", "print(\"✅ 文本预处理模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键词提取模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TextRank4zh提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    if not TEXTRANK4ZH_AVAILABLE:\n", "        return []\n", "    \n", "    try:\n", "        tr4w = TextRank4Keyword()\n", "        tr4w.analyze(text=text, lower=True, window=2)\n", "        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)\n", "        return [(item.word, item.weight) for item in keywords]\n", "    except Exception as e:\n", "        print(f\"TextRank4zh提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用jieba的TextRank提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)\n", "        return list(keywords)\n", "    except Exception as e:\n", "        print(f\"jieba TextRank提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TF-IDF提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        # 分词\n", "        words = jieba.lcut(text)\n", "        text_processed = ' '.join(words)\n", "        \n", "        # TF-IDF\n", "        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))\n", "        tfidf_matrix = vectorizer.fit_transform([text_processed])\n", "        \n", "        # 获取特征名和权重\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        tfidf_scores = tfidf_matrix.toarray()[0]\n", "        \n", "        # 排序并返回前num_keywords个\n", "        word_scores = list(zip(feature_names, tfidf_scores))\n", "        word_scores.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        return word_scores[:num_keywords]\n", "    except Exception as e:\n", "        print(f\"TF-IDF提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    综合多种方法提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    print(\"🔍 开始提取关键词...\")\n", "    \n", "    all_keywords = defaultdict(float)\n", "    \n", "    # 方法1: TextRank4zh\n", "    if TEXTRANK4ZH_AVAILABLE:\n", "        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)\n", "        if keywords_tr4zh:\n", "            print(f\"✅ TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词\")\n", "            for word, weight in keywords_tr4zh:\n", "                all_keywords[word] += weight * 0.4\n", "    \n", "    # 方法2: <PERSON><PERSON><PERSON> TextRank\n", "    keywords_jieba = extract_keywords_jieba(text, num_keywords)\n", "    if keywords_jieba:\n", "        print(f\"✅ jieba TextRank提取到 {len(keywords_jieba)} 个关键词\")\n", "        for word, weight in keywords_jieba:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 方法3: TF-IDF\n", "    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)\n", "    if keywords_tfidf:\n", "        print(f\"✅ TF-IDF提取到 {len(keywords_tfidf)} 个关键词\")\n", "        for word, weight in keywords_tfidf:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 合并并排序\n", "    if not all_keywords:\n", "        print(\"❌ 所有方法都未能提取到关键词\")\n", "        return []\n", "    \n", "    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)\n", "    result = sorted_keywords[:num_keywords]\n", "    \n", "    print(f\"✅ 关键词提取完成，共 {len(result)} 个关键词\")\n", "    \n", "    return result\n", "\n", "def extract_keywords_with_sentences(text: str, keywords: List[Tuple[str, float]], num_sentences: int = 2) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    提取包含关键词的代表性句子\n", "    \n", "    参数:\n", "        text: 原始文本\n", "        keywords: 关键词列表\n", "        num_sentences: 每个关键词返回的句子数量\n", "    \n", "    返回:\n", "        关键词到句子列表的映射\n", "    \"\"\"\n", "    # 分割句子\n", "    sentences = re.split(r'[。！？!?；;]+', text)\n", "    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]\n", "    \n", "    keyword_sentences = {}\n", "    \n", "    for keyword, weight in keywords:\n", "        matching_sentences = []\n", "        \n", "        for sentence in sentences:\n", "            if keyword in sentence:\n", "                matching_sentences.append(sentence)\n", "        \n", "        # 按句子长度排序，选择较长的句子作为代表性句子\n", "        matching_sentences.sort(key=len, reverse=True)\n", "        keyword_sentences[keyword] = matching_sentences[:num_sentences]\n", "    \n", "    return keyword_sentences\n", "\n", "print(\"✅ 关键词提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 情感分析模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\n", "    \"\"\"\n", "    加载情感词典\n", "    \n", "    参数:\n", "        positive_path: 正面词典文件路径\n", "        negative_path: 负面词典文件路径\n", "    \n", "    返回:\n", "        情感词典，正面词为正值，负面词为负值\n", "    \"\"\"\n", "    sentiment_dict = {}\n", "    \n", "    # 默认情感词\n", "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定']\n", "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定']\n", "    \n", "    # 添加默认词\n", "    for word in default_positive:\n", "        sentiment_dict[word] = 1.0\n", "    for word in default_negative:\n", "        sentiment_dict[word] = -1.0\n", "    \n", "    # 加载正面词典\n", "    if os.path.exists(positive_path):\n", "        try:\n", "            if positive_path.endswith('.csv'):\n", "                df = pd.read_csv(positive_path)\n", "                if not df.empty:\n", "                    words = df.iloc[:, 0].tolist()\n", "                    for word in words:\n", "                        if isinstance(word, str) and word.strip():\n", "                            sentiment_dict[word.strip()] = 1.0\n", "            else:\n", "                with open(positive_path, 'r', encoding='utf-8') as f:\n", "                    for line in f:\n", "                        word = line.strip()\n", "                        if word:\n", "                            sentiment_dict[word] = 1.0\n", "            print(f\"✅ 加载正面词典: {positive_path}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载正面词典失败: {e}\")\n", "    \n", "    # 加载负面词典\n", "    if os.path.exists(negative_path):\n", "        try:\n", "            if negative_path.endswith('.csv'):\n", "                df = pd.read_csv(negative_path)\n", "                if not df.empty:\n", "                    words = df.iloc[:, 0].tolist()\n", "                    for word in words:\n", "                        if isinstance(word, str) and word.strip():\n", "                            sentiment_dict[word.strip()] = -1.0\n", "            else:\n", "                with open(negative_path, 'r', encoding='utf-8') as f:\n", "                    for line in f:\n", "                        word = line.strip()\n", "                        if word:\n", "                            sentiment_dict[word] = -1.0\n", "            print(f\"✅ 加载负面词典: {negative_path}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载负面词典失败: {e}\")\n", "    \n", "    print(f\"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词\")\n", "    return sentiment_dict\n", "\n", "def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:\n", "    \"\"\"\n", "    基于情感词典的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "        sentiment_dict: 情感词典\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表, 匹配的情感词列表)\n", "    \"\"\"\n", "    print(\"📊 开始基于词典的情感分析...\")\n", "    \n", "    # 分词\n", "    words = jieba.lcut(text)\n", "    \n", "    # 计算整体情感得分\n", "    total_score = 0\n", "    matched_words = []\n", "    \n", "    for word in words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            total_score += score\n", "            matched_words.append((word, score))\n", "    \n", "    # 归一化整体得分\n", "    if len(words) > 0:\n", "        overall_score = total_score / len(words)\n", "    else:\n", "        overall_score = 0\n", "    \n", "    # 计算关键词情感得分\n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        if keyword in sentiment_dict:\n", "            score = sentiment_dict[keyword]\n", "        else:\n", "            # 如果关键词不在词典中，检查是否包含情感词\n", "            score = 0\n", "            for word in sentiment_dict:\n", "                if word in keyword:\n", "                    score += sentiment_dict[word] * 0.5\n", "        \n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"✅ 词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词\")\n", "    \n", "    return overall_score, keyword_scores, matched_words\n", "\n", "print(\"✅ 情感分析模块已定义\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}