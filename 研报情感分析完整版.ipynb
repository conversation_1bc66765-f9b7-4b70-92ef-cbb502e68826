{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 完整版\n", "\n", "本notebook整合了研报情感分析的完整流程，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 情感分析（词典法 + FinBERT）\n", "5. 结果可视化\n", "\n", "所有结果都在notebook内展示，无需外部文件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和模块"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Failed to import transformers.models.auto.tokenization_auto because of the following error (look up to see its traceback):\nFailed to import transformers.generation.utils because of the following error (look up to see its traceback):\ndeprecated() got an unexpected keyword argument 'name'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1967\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1966\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32md:\\anaconda3\\Lib\\importlib\\__init__.py:90\u001b[0m, in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m     89\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m---> 90\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _bootstrap\u001b[38;5;241m.\u001b[39m_gcd_import(name[level:], package, level)\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1387\u001b[0m, in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1360\u001b[0m, in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1331\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:935\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[1;34m(spec)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap_external>:995\u001b[0m, in \u001b[0;36mexec_module\u001b[1;34m(self, module)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:488\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[1;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\generation\\utils.py:121\u001b[0m\n\u001b[0;32m    120\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_accelerate_available():\n\u001b[1;32m--> 121\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01maccelerate\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mhooks\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AlignDevicesHook, add_hook_to_module\n\u001b[0;32m    124\u001b[0m \u001b[38;5;66;03m# Variable names used to hold the cache at generation time\u001b[39;00m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\__init__.py:16\u001b[0m\n\u001b[0;32m     14\u001b[0m __version__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m1.6.0\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m---> 16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01maccelerator\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Accelerator\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbig_modeling\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     18\u001b[0m     cpu_offload,\n\u001b[0;32m     19\u001b[0m     cpu_offload_with_hook,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     24\u001b[0m     load_checkpoint_and_dispatch,\n\u001b[0;32m     25\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\accelerator.py:36\u001b[0m\n\u001b[0;32m     34\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mhuggingface_hub\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m split_torch_state_dict_into_shards\n\u001b[1;32m---> 36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01maccelerate\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimports\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m is_torchao_available\n\u001b[0;32m     38\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcheckpointing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m load_accelerator_state, load_custom_state, save_accelerator_state, save_custom_state\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\utils\\__init__.py:218\u001b[0m\n\u001b[0;32m    207\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdeepspeed\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m    208\u001b[0m         DeepSpeedEngineWrapper,\n\u001b[0;32m    209\u001b[0m         DeepSpeedOptimizerWrapper,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    215\u001b[0m         map_pytorch_optim_to_deepspeed,\n\u001b[0;32m    216\u001b[0m     )\n\u001b[1;32m--> 218\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbnb\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m has_4bit_bnb_layers, load_and_quantize_model\n\u001b[0;32m    219\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfsdp_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m    220\u001b[0m     disable_fsdp_ram_efficient_loading,\n\u001b[0;32m    221\u001b[0m     enable_fsdp_ram_efficient_loading,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    231\u001b[0m     save_fsdp_optimizer,\n\u001b[0;32m    232\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\utils\\bnb.py:29\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01maccelerate\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimports\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     25\u001b[0m     is_4bit_bnb_available,\n\u001b[0;32m     26\u001b[0m     is_8bit_bnb_available,\n\u001b[0;32m     27\u001b[0m )\n\u001b[1;32m---> 29\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbig_modeling\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m dispatch_model, init_empty_weights\n\u001b[0;32m     30\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdataclasses\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BnbQuantizationConfig\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\big_modeling.py:24\u001b[0m\n\u001b[0;32m     22\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnn\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnn\u001b[39;00m\n\u001b[1;32m---> 24\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mhooks\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     25\u001b[0m     AlignDevicesHook,\n\u001b[0;32m     26\u001b[0m     CpuOffload,\n\u001b[0;32m     27\u001b[0m     UserCpuOffloadHook,\n\u001b[0;32m     28\u001b[0m     add_hook_to_module,\n\u001b[0;32m     29\u001b[0m     attach_align_device_hook,\n\u001b[0;32m     30\u001b[0m     attach_align_device_hook_on_blocks,\n\u001b[0;32m     31\u001b[0m )\n\u001b[0;32m     32\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     33\u001b[0m     OffloadedWeightsLoader,\n\u001b[0;32m     34\u001b[0m     check_cuda_p2p_ib_support,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     49\u001b[0m     retie_parameters,\n\u001b[0;32m     50\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\hooks.py:37\u001b[0m\n\u001b[0;32m     36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodeling\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_non_persistent_buffers\n\u001b[1;32m---> 37\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mother\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m recursive_getattr\n\u001b[0;32m     40\u001b[0m _accelerate_added_attributes \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mto\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcuda\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnpu\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mxpu\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmlu\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msdaa\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmusa\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\utils\\other.py:29\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msafetensors\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtorch\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m save_file \u001b[38;5;28;01mas\u001b[39;00m safe_save_file\n\u001b[1;32m---> 29\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcommands\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdefault\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m write_basic_config  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n\u001b[0;32m     30\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlogging\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_logger\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\commands\\config\\__init__.py:19\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01marg<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m---> 19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m config_command_parser\n\u001b[0;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig_args\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m default_config_file, load_config_from_file  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\commands\\config\\config.py:25\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _ask_field, _ask_options, _convert_compute_environment  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n\u001b[1;32m---> 25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msagemaker\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_sagemaker_input\n\u001b[0;32m     28\u001b[0m description \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLaunches a series of prompts to create and save a `default_config.yaml` configuration file for your training system. Should always be ran first on your machine\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\accelerate\\commands\\config\\sagemaker.py:35\u001b[0m\n\u001b[0;32m     34\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_boto3_available():\n\u001b[1;32m---> 35\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mboto3\u001b[39;00m  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n\u001b[0;32m     38\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_create_iam_role_for_sagemaker\u001b[39m(role_name):\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\boto3\\__init__.py:17\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mboto3\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _warn_deprecated_python\n\u001b[1;32m---> 17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mboto3\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msession\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Session\n\u001b[0;32m     19\u001b[0m __author__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAmazon Web Services\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\boto3\\session.py:17\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m---> 17\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msession\u001b[39;00m\n\u001b[0;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mclient\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Config\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\session.py:26\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mwarnings\u001b[39;00m\n\u001b[1;32m---> 26\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mclient\u001b[39;00m\n\u001b[0;32m     27\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfigloader\u001b[39;00m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\client.py:15\u001b[0m\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mlogging\u001b[39;00m\n\u001b[1;32m---> 15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m waiter, xform_name\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01margs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ClientArgsCreator\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\waiter.py:18\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m---> 18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocstring\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m WaiterDocstring\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_service_module_name\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\docs\\__init__.py:15\u001b[0m\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m---> 15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mservice\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ServiceDocumenter\n\u001b[0;32m     17\u001b[0m DEPRECATED_SERVICE_NAMES \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msms-voice\u001b[39m\u001b[38;5;124m'\u001b[39m}\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\docs\\service.py:14\u001b[0m\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbcdoc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mrestdoc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DocumentStructure\n\u001b[1;32m---> 14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     15\u001b[0m     ClientContextParamsDocumenter,\n\u001b[0;32m     16\u001b[0m     ClientDocumenter,\n\u001b[0;32m     17\u001b[0m     ClientExceptionsDocumenter,\n\u001b[0;32m     18\u001b[0m )\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpaginator\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PaginatorDocumenter\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\docs\\client.py:18\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbcdoc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mrestdoc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DocumentStructure\n\u001b[1;32m---> 18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01me<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ResponseExampleDocumenter\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmethod\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     20\u001b[0m     document_custom_method,\n\u001b[0;32m     21\u001b[0m     document_model_driven_method,\n\u001b[0;32m     22\u001b[0m     get_instance_public_methods,\n\u001b[0;32m     23\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\docs\\example.py:13\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Copyright 2015 Amazon.com, Inc. or its affiliates. All Rights Reserved.\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\"). You\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[38;5;66;03m# ANY KIND, either express or implied. See the License for the specific\u001b[39;00m\n\u001b[0;32m     12\u001b[0m \u001b[38;5;66;03m# language governing permissions and limitations under the License.\u001b[39;00m\n\u001b[1;32m---> 13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mshape\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ShapeDocumenter\n\u001b[0;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m py_default\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\docs\\shape.py:19\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Copyright 2015 Amazon.com, Inc. or its affiliates. All Rights Reserved.\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\"). You\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;66;03m# inherited from a Documenter class with the appropriate methods\u001b[39;00m\n\u001b[0;32m     18\u001b[0m \u001b[38;5;66;03m# and attributes.\u001b[39;00m\n\u001b[1;32m---> 19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m is_json_value_header\n\u001b[0;32m     22\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mShapeDocumenter\u001b[39;00m:\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\utils.py:39\u001b[0m\n\u001b[0;32m     38\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mawsrequest\u001b[39;00m\n\u001b[1;32m---> 39\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mbotocore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mhttpsession\u001b[39;00m\n\u001b[0;32m     41\u001b[0m \u001b[38;5;66;03m# IP Regexes retained for backwards compatibility\u001b[39;00m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\botocore\\httpsession.py:45\u001b[0m\n\u001b[0;32m     44\u001b[0m         \u001b[38;5;66;03m# Always import the original SSLContext, even if it has been patched\u001b[39;00m\n\u001b[1;32m---> 45\u001b[0m         \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01murllib3\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcontrib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyopenssl\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     46\u001b[0m             orig_util_SSLContext \u001b[38;5;28;01mas\u001b[39;00m SSLContext,\n\u001b[0;32m     47\u001b[0m         )\n\u001b[0;32m     48\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py:50\u001b[0m\n\u001b[0;32m     48\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m__future__\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m absolute_import\n\u001b[1;32m---> 50\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mOpenSSL\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcrypto\u001b[39;00m\n\u001b[0;32m     51\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mOpenSSL\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mSSL\u001b[39;00m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\OpenSSL\\__init__.py:8\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;124;03mpyOpenSSL - A simple wrapper around the OpenSSL library\u001b[39;00m\n\u001b[0;32m      6\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m----> 8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mOpenSSL\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SSL, crypto\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mOpenSSL\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mversion\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     10\u001b[0m     __author__,\n\u001b[0;32m     11\u001b[0m     __copyright__,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     17\u001b[0m     __version__,\n\u001b[0;32m     18\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\OpenSSL\\SSL.py:35\u001b[0m\n\u001b[0;32m     32\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mOpenSSL\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_util\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     33\u001b[0m     text_to_bytes_and_warn \u001b[38;5;28;01mas\u001b[39;00m _text_to_bytes_and_warn,\n\u001b[0;32m     34\u001b[0m )\n\u001b[1;32m---> 35\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mOpenSSL\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcrypto\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     36\u001b[0m     FILETYPE_PEM,\n\u001b[0;32m     37\u001b[0m     X509,\n\u001b[0;32m     38\u001b[0m     <PERSON><PERSON>,\n\u001b[0;32m     39\u001b[0m     X509Name,\n\u001b[0;32m     40\u001b[0m     X509Store,\n\u001b[0;32m     41\u001b[0m     _EllipticCurve,\n\u001b[0;32m     42\u001b[0m     _PassphraseHelper,\n\u001b[0;32m     43\u001b[0m )\n\u001b[0;32m     45\u001b[0m __all__ \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m     46\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mOPENSSL_VERSION_NUMBER\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     47\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSSLEAY_VERSION\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    143\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX509VerificationCodes\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    144\u001b[0m ]\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\OpenSSL\\crypto.py:963\u001b[0m\n\u001b[0;32m    962\u001b[0m _X509ExtensionInternal \u001b[38;5;241m=\u001b[39m X509Extension\n\u001b[1;32m--> 963\u001b[0m utils\u001b[38;5;241m.\u001b[39mdeprecated(\n\u001b[0;32m    964\u001b[0m     X509Extension,\n\u001b[0;32m    965\u001b[0m     \u001b[38;5;18m__name__\u001b[39m,\n\u001b[0;32m    966\u001b[0m     (\n\u001b[0;32m    967\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX509Extension support in pyOpenSSL is deprecated. You should use the \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    968\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAPIs in cryptography.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    969\u001b[0m     ),\n\u001b[0;32m    970\u001b[0m     \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m,\n\u001b[0;32m    971\u001b[0m     name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX509Extension\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    972\u001b[0m )\n\u001b[0;32m    975\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mX509Req\u001b[39;00m:\n", "\u001b[1;31mTypeError\u001b[0m: deprecated() got an unexpected keyword argument 'name'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1967\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1966\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32md:\\anaconda3\\Lib\\importlib\\__init__.py:90\u001b[0m, in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m     89\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m---> 90\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _bootstrap\u001b[38;5;241m.\u001b[39m_gcd_import(name[level:], package, level)\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1387\u001b[0m, in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1360\u001b[0m, in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1331\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:935\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[1;34m(spec)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap_external>:995\u001b[0m, in \u001b[0;36mexec_module\u001b[1;34m(self, module)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:488\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[1;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\models\\auto\\tokenization_auto.py:38\u001b[0m\n\u001b[0;32m     37\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mencoder_decoder\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m EncoderDecoderConfig\n\u001b[1;32m---> 38\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mauto_factory\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _LazyAutoMapping\n\u001b[0;32m     39\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfiguration_auto\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     40\u001b[0m     CONFIG_MAPPING_NAMES,\n\u001b[0;32m     41\u001b[0m     AutoConfig,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     44\u001b[0m     replace_list_option_in_docstrings,\n\u001b[0;32m     45\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\models\\auto\\auto_factory.py:40\u001b[0m\n\u001b[0;32m     39\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_torch_available():\n\u001b[1;32m---> 40\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgeneration\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m GenerationMixin\n\u001b[0;32m     43\u001b[0m logger \u001b[38;5;241m=\u001b[39m logging\u001b[38;5;241m.\u001b[39mget_logger(\u001b[38;5;18m__name__\u001b[39m)\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1412\u001b[0m, in \u001b[0;36m_handle_fromlist\u001b[1;34m(module, fromlist, import_, recursive)\u001b[0m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1955\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1954\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m-> 1955\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module[name])\n\u001b[0;32m   1956\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1969\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m-> 1969\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m   1970\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodule_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m because of the following error (look up to see its\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1971\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m traceback):\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1972\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[1;31mRuntimeError\u001b[0m: Failed to import transformers.generation.utils because of the following error (look up to see its traceback):\ndeprecated() got an unexpected keyword argument 'name'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 51\u001b[0m\n\u001b[0;32m     49\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m     50\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[1;32m---> 51\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutoTokenizer, AutoModelForSequenceClassification\n\u001b[0;32m     52\u001b[0m     TRANSFORMERS_AVAILABLE \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m     53\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1412\u001b[0m, in \u001b[0;36m_handle_fromlist\u001b[1;34m(module, fromlist, import_, recursive)\u001b[0m\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1956\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1954\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[0;32m   1955\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module[name])\n\u001b[1;32m-> 1956\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[0;32m   1957\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_modules:\n\u001b[0;32m   1958\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(name)\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1955\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1953\u001b[0m     value \u001b[38;5;241m=\u001b[39m Placeholder\n\u001b[0;32m   1954\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m-> 1955\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module[name])\n\u001b[0;32m   1956\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[0;32m   1957\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_modules:\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\transformers\\utils\\import_utils.py:1969\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m-> 1969\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m   1970\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodule_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m because of the following error (look up to see its\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1971\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m traceback):\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1972\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[1;31mRuntimeError\u001b[0m: Failed to import transformers.models.auto.tokenization_auto because of the following error (look up to see its traceback):\nFailed to import transformers.generation.utils because of the following error (look up to see its traceback):\ndeprecated() got an unexpected keyword argument 'name'"]}], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "try:\n", "    import camelot\n", "    CAMELOT_AVAILABLE = True\n", "except ImportError:\n", "    CAMELOT_AVAILABLE = False\n", "    print(\"警告: camelot-py不可用，高级表格提取功能将不可用\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 深度学习\n", "try:\n", "    import torch\n", "    from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "    TRANSFORMERS_AVAILABLE = True\n", "except ImportError:\n", "    TRANSFORMERS_AVAILABLE = False\n", "    print(\"警告: transformers不可用，FinBERT功能将不可用\")\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib使用系统默认字体，避免中文显示问题\n", "plt.rcParams['font.family'] = ['sans-serif']\n", "plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ 所有必要的库已导入完成\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"Camelot可用: {CAMELOT_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"Transformers可用: {TRANSFORMERS_AVAILABLE}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PDF文本提取模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:\n", "    \"\"\"\n", "    使用PyMuPDF提取PDF文本\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 按页分割的文本列表)\n", "    \"\"\"\n", "    if not PYMUPDF_AVAILABLE:\n", "        return None, []\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        all_text = \"\"\n", "        page_texts = []\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc[page_num]\n", "            text = page.get_text()\n", "            \n", "            if text.strip():\n", "                all_text += text + \"\\n\"\n", "                page_texts.append(text)\n", "        \n", "        doc.close()\n", "        return all_text, page_texts\n", "        \n", "    except Exception as e:\n", "        print(f\"PyMuPDF提取失败: {e}\")\n", "        return None, []\n", "\n", "def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    使用pdfplumber提取PDF文本和表格\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 表格列表)\n", "    \"\"\"\n", "    try:\n", "        all_text = \"\"\n", "        all_tables = []\n", "        \n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            for page_num, page in enumerate(pdf.pages):\n", "                # 提取文本\n", "                text = page.extract_text()\n", "                if text:\n", "                    all_text += text + \"\\n\"\n", "                \n", "                # 提取表格\n", "                tables = page.extract_tables()\n", "                for table in tables:\n", "                    if table and len(table) > 1:\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            all_tables.append(df)\n", "                        except Exception as e:\n", "                            print(f\"表格处理失败: {e}\")\n", "        \n", "        return all_text, all_tables\n", "        \n", "    except Exception as e:\n", "        print(f\"pdfplumber提取失败: {e}\")\n", "        return \"\", []\n", "\n", "def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    从PDF文件中提取文本和表格，使用多种方法确保提取完整性\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (提取的文本, 表格列表)\n", "    \"\"\"\n", "    print(f\"📄 开始提取PDF文件: {os.path.basename(pdf_path)}\")\n", "    \n", "    # 方法1: PyMuPDF提取文本\n", "    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)\n", "    \n", "    # 方法2: pdfplumber提取文本和表格\n", "    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)\n", "    \n", "    # 选择最佳文本提取结果\n", "    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):\n", "        best_text = pymupdf_text\n", "        print(f\"✅ 使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)\")\n", "    else:\n", "        best_text = pdfplumber_text\n", "        print(f\"✅ 使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)\")\n", "    \n", "    print(f\"📊 提取到 {len(tables)} 个表格\")\n", "    \n", "    return best_text, tables\n", "\n", "print(\"✅ PDF文本提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 文本预处理模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_stopwords(stopwords_path: str) -> set:\n", "    \"\"\"\n", "    加载停用词\n", "    \n", "    参数:\n", "        stopwords_path: 停用词文件路径\n", "    \n", "    返回:\n", "        停用词集合\n", "    \"\"\"\n", "    stopwords = set()\n", "    \n", "    # 默认停用词\n", "    default_stopwords = {\n", "        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',\n", "        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'\n", "    }\n", "    stopwords.update(default_stopwords)\n", "    \n", "    # 从文件加载停用词\n", "    if os.path.exists(stopwords_path):\n", "        try:\n", "            with open(stopwords_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    word = line.strip()\n", "                    if word:\n", "                        stopwords.add(word)\n", "            print(f\"✅ 从文件加载了 {len(stopwords)} 个停用词\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载停用词文件失败: {e}，使用默认停用词\")\n", "    else:\n", "        print(f\"⚠️ 停用词文件不存在: {stopwords_path}，使用默认停用词\")\n", "    \n", "    return stopwords\n", "\n", "def clean_text(text: str) -> str:\n", "    \"\"\"\n", "    清洗文本，去除特殊字符等\n", "    \n", "    参数:\n", "        text: 待清洗的文本\n", "    \n", "    返回:\n", "        清洗后的文本\n", "    \"\"\"\n", "    # 去除URL\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', '', text)\n", "    \n", "    # 去除HTML标签\n", "    text = re.sub(r'<.*?>', '', text)\n", "    \n", "    # 去除邮箱\n", "    text = re.sub(r'\\S*@\\S*\\s?', '', text)\n", "    \n", "    # 保留中文、英文、数字和基本标点\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9.,，。、；：''\"\"（）()？?!！\\s]+', ' ', text)\n", "    \n", "    # 去除多余的空白字符\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:\n", "    \"\"\"\n", "    文本预处理：分词、去停用词、过滤\n", "    \n", "    参数:\n", "        text: 待处理的文本\n", "        stopwords_path: 停用词文件路径\n", "        min_word_len: 最小词长度\n", "    \n", "    返回:\n", "        (过滤后的词列表, 过滤后的文本)\n", "    \"\"\"\n", "    print(\"🔄 开始文本预处理...\")\n", "    \n", "    if not text or len(text.strip()) == 0:\n", "        print(\"❌ 输入文本为空\")\n", "        return [], \"\"\n", "    \n", "    # 清洗文本\n", "    cleaned_text = clean_text(text)\n", "    print(f\"📝 文本清洗完成，长度: {len(cleaned_text)} 字符\")\n", "    \n", "    # 加载停用词\n", "    stopwords = load_stopwords(stopwords_path)\n", "    \n", "    # 分词\n", "    print(\"✂️ 开始分词...\")\n", "    words = jieba.lcut(cleaned_text)\n", "    print(f\"📊 分词完成，共 {len(words)} 个词\")\n", "    \n", "    # 过滤词语\n", "    filtered_words = []\n", "    for word in words:\n", "        word = word.strip()\n", "        if (len(word) >= min_word_len and \n", "            word not in stopwords and \n", "            not word.isdigit() and \n", "            not re.match(r'^[\\W_]+$', word)):\n", "            filtered_words.append(word)\n", "    \n", "    # 重新组合文本\n", "    filtered_text = ' '.join(filtered_words)\n", "    \n", "    print(f\"✅ 文本预处理完成，过滤后共 {len(filtered_words)} 个有效词\")\n", "    \n", "    return filtered_words, filtered_text\n", "\n", "print(\"✅ 文本预处理模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键词提取模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TextRank4zh提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    if not TEXTRANK4ZH_AVAILABLE:\n", "        return []\n", "    \n", "    try:\n", "        tr4w = TextRank4Keyword()\n", "        tr4w.analyze(text=text, lower=True, window=2)\n", "        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)\n", "        return [(item.word, item.weight) for item in keywords]\n", "    except Exception as e:\n", "        print(f\"TextRank4zh提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用jieba的TextRank提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)\n", "        return list(keywords)\n", "    except Exception as e:\n", "        print(f\"jieba TextRank提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TF-IDF提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        # 分词\n", "        words = jieba.lcut(text)\n", "        text_processed = ' '.join(words)\n", "        \n", "        # TF-IDF\n", "        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))\n", "        tfidf_matrix = vectorizer.fit_transform([text_processed])\n", "        \n", "        # 获取特征名和权重\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        tfidf_scores = tfidf_matrix.toarray()[0]\n", "        \n", "        # 排序并返回前num_keywords个\n", "        word_scores = list(zip(feature_names, tfidf_scores))\n", "        word_scores.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        return word_scores[:num_keywords]\n", "    except Exception as e:\n", "        print(f\"TF-IDF提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    综合多种方法提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    print(\"🔍 开始提取关键词...\")\n", "    \n", "    all_keywords = defaultdict(float)\n", "    \n", "    # 方法1: TextRank4zh\n", "    if TEXTRANK4ZH_AVAILABLE:\n", "        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)\n", "        if keywords_tr4zh:\n", "            print(f\"✅ TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词\")\n", "            for word, weight in keywords_tr4zh:\n", "                all_keywords[word] += weight * 0.4\n", "    \n", "    # 方法2: <PERSON><PERSON><PERSON> TextRank\n", "    keywords_jieba = extract_keywords_jieba(text, num_keywords)\n", "    if keywords_jieba:\n", "        print(f\"✅ jieba TextRank提取到 {len(keywords_jieba)} 个关键词\")\n", "        for word, weight in keywords_jieba:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 方法3: TF-IDF\n", "    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)\n", "    if keywords_tfidf:\n", "        print(f\"✅ TF-IDF提取到 {len(keywords_tfidf)} 个关键词\")\n", "        for word, weight in keywords_tfidf:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 合并并排序\n", "    if not all_keywords:\n", "        print(\"❌ 所有方法都未能提取到关键词\")\n", "        return []\n", "    \n", "    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)\n", "    result = sorted_keywords[:num_keywords]\n", "    \n", "    print(f\"✅ 关键词提取完成，共 {len(result)} 个关键词\")\n", "    \n", "    return result\n", "\n", "def extract_keywords_with_sentences(text: str, keywords: List[Tuple[str, float]], num_sentences: int = 2) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    提取包含关键词的代表性句子\n", "    \n", "    参数:\n", "        text: 原始文本\n", "        keywords: 关键词列表\n", "        num_sentences: 每个关键词返回的句子数量\n", "    \n", "    返回:\n", "        关键词到句子列表的映射\n", "    \"\"\"\n", "    # 分割句子\n", "    sentences = re.split(r'[。！？!?；;]+', text)\n", "    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]\n", "    \n", "    keyword_sentences = {}\n", "    \n", "    for keyword, weight in keywords:\n", "        matching_sentences = []\n", "        \n", "        for sentence in sentences:\n", "            if keyword in sentence:\n", "                matching_sentences.append(sentence)\n", "        \n", "        # 按句子长度排序，选择较长的句子作为代表性句子\n", "        matching_sentences.sort(key=len, reverse=True)\n", "        keyword_sentences[keyword] = matching_sentences[:num_sentences]\n", "    \n", "    return keyword_sentences\n", "\n", "print(\"✅ 关键词提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 情感分析模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\n", "    \"\"\"\n", "    加载情感词典\n", "    \n", "    参数:\n", "        positive_path: 正面词典文件路径\n", "        negative_path: 负面词典文件路径\n", "    \n", "    返回:\n", "        情感词典，正面词为正值，负面词为负值\n", "    \"\"\"\n", "    sentiment_dict = {}\n", "    \n", "    # 默认情感词\n", "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定']\n", "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定']\n", "    \n", "    # 添加默认词\n", "    for word in default_positive:\n", "        sentiment_dict[word] = 1.0\n", "    for word in default_negative:\n", "        sentiment_dict[word] = -1.0\n", "    \n", "    # 加载正面词典\n", "    if os.path.exists(positive_path):\n", "        try:\n", "            if positive_path.endswith('.csv'):\n", "                df = pd.read_csv(positive_path)\n", "                if not df.empty:\n", "                    words = df.iloc[:, 0].tolist()\n", "                    for word in words:\n", "                        if isinstance(word, str) and word.strip():\n", "                            sentiment_dict[word.strip()] = 1.0\n", "            else:\n", "                with open(positive_path, 'r', encoding='utf-8') as f:\n", "                    for line in f:\n", "                        word = line.strip()\n", "                        if word:\n", "                            sentiment_dict[word] = 1.0\n", "            print(f\"✅ 加载正面词典: {positive_path}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载正面词典失败: {e}\")\n", "    \n", "    # 加载负面词典\n", "    if os.path.exists(negative_path):\n", "        try:\n", "            if negative_path.endswith('.csv'):\n", "                df = pd.read_csv(negative_path)\n", "                if not df.empty:\n", "                    words = df.iloc[:, 0].tolist()\n", "                    for word in words:\n", "                        if isinstance(word, str) and word.strip():\n", "                            sentiment_dict[word.strip()] = -1.0\n", "            else:\n", "                with open(negative_path, 'r', encoding='utf-8') as f:\n", "                    for line in f:\n", "                        word = line.strip()\n", "                        if word:\n", "                            sentiment_dict[word] = -1.0\n", "            print(f\"✅ 加载负面词典: {negative_path}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载负面词典失败: {e}\")\n", "    \n", "    print(f\"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词\")\n", "    return sentiment_dict\n", "\n", "def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:\n", "    \"\"\"\n", "    基于情感词典的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "        sentiment_dict: 情感词典\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表, 匹配的情感词列表)\n", "    \"\"\"\n", "    print(\"📊 开始基于词典的情感分析...\")\n", "    \n", "    # 分词\n", "    words = jieba.lcut(text)\n", "    \n", "    # 计算整体情感得分\n", "    total_score = 0\n", "    matched_words = []\n", "    \n", "    for word in words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            total_score += score\n", "            matched_words.append((word, score))\n", "    \n", "    # 归一化整体得分\n", "    if len(words) > 0:\n", "        overall_score = total_score / len(words)\n", "    else:\n", "        overall_score = 0\n", "    \n", "    # 计算关键词情感得分\n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        if keyword in sentiment_dict:\n", "            score = sentiment_dict[keyword]\n", "        else:\n", "            # 如果关键词不在词典中，检查是否包含情感词\n", "            score = 0\n", "            for word in sentiment_dict:\n", "                if word in keyword:\n", "                    score += sentiment_dict[word] * 0.5\n", "        \n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"✅ 词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词\")\n", "    \n", "    return overall_score, keyword_scores, matched_words\n", "\n", "print(\"✅ 情感分析模块已定义\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sentiment_analysis_by_fin<PERSON>(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    基于FinBERT的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    if not TRANSFORMERS_AVAILABLE:\n", "        print(\"⚠️ transformers不可用，使用简化的情感分析\")\n", "        return sentiment_analysis_by_snownlp(text, keywords)\n", "    \n", "    print(\"🤖 开始基于FinBERT的情感分析...\")\n", "    \n", "    try:\n", "        # 检查本地FinBERT模型\n", "        finbert_dir = os.path.join('src', 'finbert')\n", "        \n", "        if os.path.exists(finbert_dir) and os.path.exists(os.path.join(finbert_dir, 'pytorch_model.bin')):\n", "            print(\"📁 使用本地FinBERT模型\")\n", "            model_path = finbert_dir\n", "        else:\n", "            print(\"🌐 使用在线FinBERT模型\")\n", "            model_path = 'ProsusAI/finbert'\n", "        \n", "        # 加载模型和分词器\n", "        tokenizer = AutoTokenizer.from_pretrained(model_path)\n", "        model = AutoModelForSequenceClassification.from_pretrained(model_path)\n", "        \n", "        # 分析整体文本\n", "        # 截断文本以适应模型输入限制\n", "        max_length = 512\n", "        if len(text) > max_length * 2:\n", "            # 取开头和结尾部分\n", "            text_for_analysis = text[:max_length] + text[-max_length:]\n", "        else:\n", "            text_for_analysis = text\n", "        \n", "        inputs = tokenizer(text_for_analysis, return_tensors='pt', truncation=True, max_length=max_length, padding=True)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(**inputs)\n", "            logits = outputs.logits\n", "            probabilities = torch.softmax(logits, dim=1)\n", "            \n", "            # FinBERT输出: [negative, neutral, positive]\n", "            # 计算情感得分: positive - negative\n", "            overall_score = probabilities[0][2].item() - probabilities[0][0].item()\n", "        \n", "        # 分析关键词情感\n", "        keyword_scores = []\n", "        \n", "        for keyword, weight in keywords:\n", "            # 为关键词创建上下文\n", "            keyword_context = f\"这个研报提到了{keyword}。{keyword}在金融分析中很重要。\"\n", "            \n", "            inputs = tokenizer(keyword_context, return_tensors='pt', truncation=True, max_length=128, padding=True)\n", "            \n", "            with torch.no_grad():\n", "                outputs = model(**inputs)\n", "                logits = outputs.logits\n", "                probabilities = torch.softmax(logits, dim=1)\n", "                \n", "                # 计算关键词情感得分\n", "                score = probabilities[0][2].item() - probabilities[0][0].item()\n", "                # 降低单独关键词的得分权重\n", "                score = score * 0.7\n", "            \n", "            keyword_scores.append((keyword, score, weight))\n", "        \n", "        print(f\"✅ FinBERT情感分析完成，整体得分: {overall_score:.4f}\")\n", "        \n", "        return overall_score, keyword_scores\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ FinBERT分析失败: {e}，使用备选方法\")\n", "        return sentiment_analysis_by_snownlp(text, keywords)\n", "\n", "def sentiment_analysis_by_snownlp(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    使用SnowNLP进行情感分析（备选方案）\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    try:\n", "        from snownlp import SnowNLP\n", "        \n", "        # 分析整体文本\n", "        s = SnowNLP(text)\n", "        # SnowNLP返回[0,1]，转换为[-1,1]\n", "        overall_score = 2 * s.sentiments - 1\n", "        \n", "        # 分析关键词\n", "        keyword_scores = []\n", "        for keyword, weight in keywords:\n", "            s = SnowNLP(keyword)\n", "            score = 2 * s.sentiments - 1\n", "            keyword_scores.append((keyword, score, weight))\n", "        \n", "        print(f\"✅ SnowNLP情感分析完成，整体得分: {overall_score:.4f}\")\n", "        return overall_score, keyword_scores\n", "        \n", "    except ImportError:\n", "        print(\"⚠️ SnowNLP不可用，使用简单规则\")\n", "        # 简单的规则基础情感分析\n", "        positive_words = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升']\n", "        negative_words = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '下降']\n", "        \n", "        words = text.split()\n", "        pos_count = sum(1 for word in words if any(pw in word for pw in positive_words))\n", "        neg_count = sum(1 for word in words if any(nw in word for nw in negative_words))\n", "        \n", "        if len(words) > 0:\n", "            overall_score = (pos_count - neg_count) / len(words)\n", "        else:\n", "            overall_score = 0\n", "        \n", "        keyword_scores = [(keyword, 0, weight) for keyword, weight in keywords]\n", "        \n", "        return overall_score, keyword_scores\n", "\n", "def compare_sentiment_results(dict_score: float, finbert_score: float, \n", "                            dict_keywords: List[Tuple[str, float, float]], \n", "                            finbert_keywords: List[Tuple[str, float, float]]) -> Tuple[pd.DataFrame, float]:\n", "    \"\"\"\n", "    比较两种情感分析方法的结果\n", "    \n", "    参数:\n", "        dict_score: 词典方法整体得分\n", "        finbert_score: FinBERT方法整体得分\n", "        dict_keywords: 词典方法关键词得分\n", "        finbert_keywords: FinBERT方法关键词得分\n", "    \n", "    返回:\n", "        (比较结果DataF<PERSON><PERSON>, 一致率)\n", "    \"\"\"\n", "    print(\"🔄 开始比较两种情感分析方法的结果...\")\n", "    \n", "    # 创建关键词映射\n", "    dict_map = {kw: (score, weight) for kw, score, weight in dict_keywords}\n", "    finbert_map = {kw: (score, weight) for kw, score, weight in finbert_keywords}\n", "    \n", "    # 获取所有关键词\n", "    all_keywords = set(dict_map.keys()) | set(finbert_map.keys())\n", "    \n", "    # 创建比较数据\n", "    comparison_data = []\n", "    \n", "    for keyword in all_keywords:\n", "        dict_score_kw, dict_weight = dict_map.get(keyword, (0, 0))\n", "        finbert_score_kw, finbert_weight = finbert_map.get(keyword, (0, 0))\n", "        \n", "        # 确定情感倾向\n", "        def get_sentiment(score):\n", "            if score > 0.1:\n", "                return '正面'\n", "            elif score < -0.1:\n", "                return '负面'\n", "            else:\n", "                return '中性'\n", "        \n", "        dict_sentiment = get_sentiment(dict_score_kw)\n", "        finbert_sentiment = get_sentiment(finbert_score_kw)\n", "        \n", "        comparison_data.append({\n", "            '关键词': keyword,\n", "            '词典情感得分': dict_score_kw,\n", "            '词典情感倾向': dict_sentiment,\n", "            'FinBERT情感得分': finbert_score_kw,\n", "            'FinBERT情感倾向': finbert_sentiment,\n", "            '权重': max(dict_weight, finbert_weight),\n", "            '得分差异': abs(dict_score_kw - finbert_score_kw),\n", "            '倾向一致': dict_sentiment == finbert_sentiment\n", "        })\n", "    \n", "    # 创建DataFrame\n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    \n", "    # 计算一致率\n", "    if len(comparison_df) > 0:\n", "        agreement_rate = comparison_df['倾向一致'].mean()\n", "    else:\n", "        agreement_rate = 0\n", "    \n", "    print(f\"✅ 情感分析比较完成，一致率: {agreement_rate:.2%}\")\n", "    \n", "    return comparison_df, agreement_rate\n", "\n", "print(\"✅ FinBERT和比较分析模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义颜色方案\n", "COLORS = {\n", "    'positive': '#2E8B57',  # 海绿色\n", "    'negative': '#DC143C',  # 深红色\n", "    'neutral': '#708090',   # 石板灰\n", "    'background': '#F8F9FA',\n", "    'highlight': '#4169E1',  # 皇家蓝\n", "    'secondary': '#FFD700'   # 金色\n", "}\n", "\n", "def create_sentiment_comparison_chart(dict_score: float, finbert_score: float) -> None:\n", "    \"\"\"\n", "    创建情感分析方法对比图表\n", "    \n", "    参数:\n", "        dict_score: 词典方法得分\n", "        finbert_score: FinBERT方法得分\n", "    \"\"\"\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    \n", "    methods = ['Dictionary Analysis', 'FinBERT Analysis']\n", "    scores = [dict_score, finbert_score]\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]\n", "    \n", "    # 创建条形图\n", "    bars = ax.bar(methods, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)\n", "    \n", "    # 添加数值标签\n", "    for bar, score in zip(bars, scores):\n", "        height = bar.get_height()\n", "        ax.text(bar.get_x() + bar.get_width()/2., height + (0.02 if height >= 0 else -0.05),\n", "                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top',\n", "                fontweight='bold', fontsize=12)\n", "    \n", "    # 添加零线\n", "    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title('Sentiment Analysis Methods Comparison', fontsize=16, fontweight='bold', pad=20)\n", "    ax.set_ylabel('Sentiment Score\\n(Negative < 0 < Positive)', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置y轴范围\n", "    y_max = max(abs(min(scores)), abs(max(scores)), 0.5)\n", "    ax.set_ylim(-y_max*1.2, y_max*1.2)\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='y', alpha=0.3, linestyle='--')\n", "    \n", "    # 添加解释文本\n", "    explanation = f\"Dictionary Score: {dict_score:.3f} ({'Positive' if dict_score > 0.1 else 'Negative' if dict_score < -0.1 else 'Neutral'})\\n\"\n", "    explanation += f\"FinBERT Score: {finbert_score:.3f} ({'Positive' if finbert_score > 0.1 else 'Negative' if finbert_score < -0.1 else 'Neutral'})\"\n", "    \n", "    plt.figtext(0.5, 0.02, explanation, ha='center', fontsize=11,\n", "                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_keyword_sentiment_chart(keyword_scores: List[Tuple[str, float, float]], method_name: str, top_n: int = 15) -> None:\n", "    \"\"\"\n", "    创建关键词情感分布图表\n", "    \n", "    参数:\n", "        keyword_scores: 关键词情感得分列表\n", "        method_name: 方法名称\n", "        top_n: 显示的关键词数量\n", "    \"\"\"\n", "    if not keyword_scores:\n", "        print(f\"No keyword sentiment data available for {method_name}\")\n", "        return\n", "    \n", "    # 按权重排序并选择前top_n个\n", "    sorted_keywords = sorted(keyword_scores, key=lambda x: x[2], reverse=True)[:top_n]\n", "    \n", "    keywords = [item[0] for item in sorted_keywords]\n", "    scores = [item[1] for item in sorted_keywords]\n", "    weights = [item[2] for item in sorted_keywords]\n", "    \n", "    # 按情感得分排序\n", "    sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])\n", "    sorted_keywords = [keywords[i] for i in sorted_indices]\n", "    sorted_scores = [scores[i] for i in sorted_indices]\n", "    sorted_weights = [weights[i] for i in sorted_indices]\n", "    \n", "    # 创建图表\n", "    fig, ax = plt.subplots(figsize=(12, 8))\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0.05 else COLORS['negative'] if s < -0.05 else COLORS['neutral'] for s in sorted_scores]\n", "    \n", "    # 创建水平条形图\n", "    bars = ax.barh(sorted_keywords, sorted_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "    \n", "    # 添加数值标签\n", "    for i, (bar, weight) in enumerate(zip(bars, sorted_weights)):\n", "        width = bar.get_width()\n", "        ax.text(width + (0.02 if width >= 0 else -0.02),\n", "                bar.get_y() + bar.get_height()/2,\n", "                f'{width:.3f}',\n", "                ha='left' if width >= 0 else 'right',\n", "                va='center', fontweight='bold', fontsize=9)\n", "    \n", "    # 添加零线\n", "    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(f'Keyword Sentiment Distribution ({method_name})', fontsize=14, fontweight='bold', pad=20)\n", "    ax.set_xlabel('Sentiment Score', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置x轴范围\n", "    if sorted_scores:\n", "        x_max = max(abs(min(sorted_scores)), abs(max(sorted_scores)), 0.3)\n", "        ax.set_xlim(-x_max*1.2, x_max*1.2)\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='x', alpha=0.3, linestyle='--')\n", "    \n", "    # 统计信息\n", "    pos_count = sum(1 for s in sorted_scores if s > 0.05)\n", "    neg_count = sum(1 for s in sorted_scores if s < -0.05)\n", "    neu_count = len(sorted_scores) - pos_count - neg_count\n", "    \n", "    stats_text = f\"Positive: {pos_count}, Negative: {neg_count}, Neutral: {neu_count}\"\n", "    plt.figtext(0.02, 0.02, stats_text, fontsize=10,\n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_agreement_chart(comparison_df: pd.DataFrame) -> None:\n", "    \"\"\"\n", "    创建两种方法一致性分析图表\n", "    \n", "    参数:\n", "        comparison_df: 比较结果DataFrame\n", "    \"\"\"\n", "    if comparison_df.empty:\n", "        print(\"No comparison data available\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 1. 一致性饼图\n", "    agreement_counts = comparison_df['倾向一致'].value_counts()\n", "    labels = ['Consistent', 'Inconsistent']\n", "    sizes = [agreement_counts.get(True, 0), agreement_counts.get(False, 0)]\n", "    colors = [COLORS['highlight'], COLORS['neutral']]\n", "    \n", "    wedges, texts, autotexts = ax1.pie(sizes, labels=labels, autopct='%1.1f%%',\n", "                                      colors=colors, startangle=90, explode=(0.05, 0))\n", "    \n", "    for autotext in autotexts:\n", "        autotext.set_fontweight('bold')\n", "    \n", "    ax1.set_title('Sentiment Analysis Agreement', fontsize=14, fontweight='bold')\n", "    \n", "    # 2. 情感分布散点图\n", "    dict_scores = comparison_df['词典情感得分'].values\n", "    finbert_scores = comparison_df['FinBERT情感得分'].values\n", "    \n", "    # 根据一致性着色\n", "    colors_scatter = [COLORS['highlight'] if agree else COLORS['secondary'] \n", "                     for agree in comparison_df['倾向一致']]\n", "    \n", "    ax2.scatter(dict_scores, finbert_scores, c=colors_scatter, alpha=0.7, s=50)\n", "    \n", "    # 添加对角线\n", "    lims = [min(ax2.get_xlim()[0], ax2.get_ylim()[0]),\n", "            max(ax2.get_xlim()[1], ax2.get_ylim()[1])]\n", "    ax2.plot(lims, lims, 'k--', alpha=0.5, zorder=0)\n", "    \n", "    # 添加象限线\n", "    ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.3)\n", "    ax2.axvline(x=0, color='gray', linestyle='-', alpha=0.3)\n", "    \n", "    ax2.set_xlabel('Dictionary Sentiment Score', fontweight='bold')\n", "    ax2.set_ylabel('FinBERT Sentiment Score', fontweight='bold')\n", "    ax2.set_title('Sentiment Score Correlation', fontsize=14, fontweight='bold')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 计算相关系数\n", "    if len(dict_scores) > 1:\n", "        correlation = np.corrcoef(dict_scores, finbert_scores)[0, 1]\n", "        ax2.text(0.05, 0.95, f'Correlation: {correlation:.3f}', \n", "                transform=ax2.transAxes, fontsize=11,\n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_wordcloud(keywords: List[Tuple[str, float]]) -> None:\n", "    \"\"\"\n", "    创建关键词云图\n", "    \n", "    参数:\n", "        keywords: 关键词列表\n", "    \"\"\"\n", "    if not keywords:\n", "        print(\"No keywords available for word cloud\")\n", "        return\n", "    \n", "    try:\n", "        # 准备词频字典\n", "        word_freq = {word: weight for word, weight in keywords}\n", "        \n", "        # 创建词云\n", "        wordcloud = WordCloud(\n", "            width=800, height=400,\n", "            background_color='white',\n", "            max_words=50,\n", "            colormap='viridis',\n", "            font_path=None  # 使用系统默认字体\n", "        ).generate_from_frequencies(word_freq)\n", "        \n", "        # 显示词云\n", "        plt.figure(figsize=(12, 6))\n", "        plt.imshow(wordcloud, interpolation='bilinear')\n", "        plt.axis('off')\n", "        plt.title('Keywords Word Cloud', fontsize=16, fontweight='bold', pad=20)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "    except Exception as e:\n", "        print(f\"Word cloud generation failed: {e}\")\n", "        # 创建简单的条形图作为替代\n", "        top_words = keywords[:20]\n", "        words = [item[0] for item in top_words]\n", "        weights = [item[1] for item in top_words]\n", "        \n", "        plt.figure(figsize=(12, 8))\n", "        plt.barh(words, weights, color=COLORS['highlight'], alpha=0.7)\n", "        plt.xlabel('Weight')\n", "        plt.title('Top Keywords (Alternative to Word Cloud)')\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "print(\"✅ 可视化模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 主流程整合"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_financial_report(pdf_path: str, \n", "                           positive_dict_path: str = None, \n", "                           negative_dict_path: str = None,\n", "                           stopwords_path: str = None,\n", "                           num_keywords: int = 20) -> Dict:\n", "    \"\"\"\n", "    完整的研报情感分析流程\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "        positive_dict_path: 正面词典路径\n", "        negative_dict_path: 负面词典路径\n", "        stopwords_path: 停用词文件路径\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        分析结果字典\n", "    \"\"\"\n", "    print(\"🚀 开始研报情感分析流程\")\n", "    print(\"=\" * 60)\n", "    \n", "    results = {\n", "        'pdf_path': pdf_path,\n", "        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'success': <PERSON><PERSON><PERSON>,\n", "        'error': None\n", "    }\n", "    \n", "    try:\n", "        # 步骤1: PDF文本提取\n", "        print(\"\\n📄 步骤1: PDF文本和表格提取\")\n", "        text, tables = extract_text_and_tables_from_pdf(pdf_path)\n", "        \n", "        if not text or len(text.strip()) < 100:\n", "            raise ValueError(\"PDF文本提取失败或内容过少\")\n", "        \n", "        results['text_length'] = len(text)\n", "        results['tables_count'] = len(tables)\n", "        results['text_sample'] = text[:500] + \"...\" if len(text) > 500 else text\n", "        \n", "        print(f\"✅ 文本提取完成: {len(text)} 字符, {len(tables)} 个表格\")\n", "        \n", "        # 步骤2: 文本预处理\n", "        print(\"\\n🔄 步骤2: 文本预处理\")\n", "        if not stopwords_path:\n", "            stopwords_path = os.path.join('data', 'stopwords.txt')\n", "        \n", "        filtered_words, filtered_text = preprocess_text(text, stopwords_path)\n", "        \n", "        if not filtered_words:\n", "            raise ValueError(\"文本预处理失败\")\n", "        \n", "        results['filtered_words_count'] = len(filtered_words)\n", "        results['filtered_text_length'] = len(filtered_text)\n", "        \n", "        # 步骤3: 关键词提取\n", "        print(\"\\n🔍 步骤3: 关键词提取\")\n", "        keywords = extract_keywords(filtered_text, num_keywords)\n", "        \n", "        if not keywords:\n", "            raise ValueError(\"关键词提取失败\")\n", "        \n", "        results['keywords'] = keywords\n", "        results['keywords_count'] = len(keywords)\n", "        \n", "        # 提取关键词代表性句子\n", "        keyword_sentences = extract_keywords_with_sentences(text, keywords)\n", "        results['keyword_sentences'] = keyword_sentences\n", "        \n", "        # 步骤4: 情感分析\n", "        print(\"\\n📊 步骤4: 情感分析\")\n", "        \n", "        # 4.1 词典方法\n", "        print(\"\\n📚 4.1 基于词典的情感分析\")\n", "        if not positive_dict_path:\n", "            positive_dict_path = os.path.join('data', 'CFSD中文金融情感词典', '正面词典.csv')\n", "        if not negative_dict_path:\n", "            negative_dict_path = os.path.join('data', 'CFSD中文金融情感词典', '负面词典.csv')\n", "        \n", "        sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)\n", "        dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(\n", "            filtered_text, keywords, sentiment_dict\n", "        )\n", "        \n", "        results['dict_analysis'] = {\n", "            'overall_score': dict_score,\n", "            'sentiment': '正面' if dict_score > 0.1 else '负面' if dict_score < -0.1 else '中性',\n", "            'keyword_scores': dict_keywords,\n", "            'matched_words': matched_words,\n", "            'matched_words_count': len(matched_words)\n", "        }\n", "        \n", "        # 4.2 FinBERT方法\n", "        print(\"\\n🤖 4.2 基于FinBERT的情感分析\")\n", "        finbert_score, finbert_keywords = sentiment_analysis_by_finbert(\n", "            filtered_text, keywords\n", "        )\n", "        \n", "        results['finbert_analysis'] = {\n", "            'overall_score': finbert_score,\n", "            'sentiment': '正面' if finbert_score > 0.1 else '负面' if finbert_score < -0.1 else '中性',\n", "            'keyword_scores': finbert_keywords\n", "        }\n", "        \n", "        # 4.3 结果比较\n", "        print(\"\\n🔄 4.3 两种方法结果比较\")\n", "        comparison_df, agreement_rate = compare_sentiment_results(\n", "            dict_score, finbert_score, dict_keywords, finbert_keywords\n", "        )\n", "        \n", "        results['comparison'] = {\n", "            'agreement_rate': agreement_rate,\n", "            'comparison_df': comparison_df,\n", "            'combined_score': (dict_score + finbert_score) / 2,\n", "            'score_difference': abs(dict_score - finbert_score)\n", "        }\n", "        \n", "        # 综合评估\n", "        combined_score = results['comparison']['combined_score']\n", "        results['final_assessment'] = {\n", "            'combined_score': combined_score,\n", "            'combined_sentiment': '正面' if combined_score > 0.1 else '负面' if combined_score < -0.1 else '中性',\n", "            'confidence': 'high' if agreement_rate > 0.7 else 'medium' if agreement_rate > 0.4 else 'low',\n", "            'agreement_rate': agreement_rate\n", "        }\n", "        \n", "        results['success'] = True\n", "        print(\"\\n✅ 研报情感分析完成!\")\n", "        \n", "        return results\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"分析过程中出错: {str(e)}\"\n", "        print(f\"\\n❌ {error_msg}\")\n", "        results['error'] = error_msg\n", "        return results\n", "\n", "def display_analysis_results(results: Dict) -> None:\n", "    \"\"\"\n", "    显示分析结果摘要\n", "    \n", "    参数:\n", "        results: 分析结果字典\n", "    \"\"\"\n", "    if not results['success']:\n", "        print(f\"❌ 分析失败: {results.get('error', '未知错误')}\")\n", "        return\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "    print(\"📊 研报情感分析结果摘要\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 基本信息\n", "    print(f\"\\n📄 文档信息:\")\n", "    print(f\"  • 文件: {os.path.basename(results['pdf_path'])}\")\n", "    print(f\"  • 分析时间: {results['timestamp']}\")\n", "    print(f\"  • 原始文本长度: {results['text_length']:,} 字符\")\n", "    print(f\"  • 提取表格数量: {results['tables_count']} 个\")\n", "    print(f\"  • 有效词汇数量: {results['filtered_words_count']:,} 个\")\n", "    print(f\"  • 关键词数量: {results['keywords_count']} 个\")\n", "    \n", "    # 关键词展示\n", "    print(f\"\\n🔍 前10个关键词:\")\n", "    for i, (word, weight) in enumerate(results['keywords'][:10], 1):\n", "        print(f\"  {i:2d}. {word:<12} (权重: {weight:.4f})\")\n", "    \n", "    # 情感分析结果\n", "    dict_analysis = results['dict_analysis']\n", "    finbert_analysis = results['finbert_analysis']\n", "    final_assessment = results['final_assessment']\n", "    \n", "    print(f\"\\n📊 情感分析结果:\")\n", "    print(f\"  • 词典方法:\")\n", "    print(f\"    - 情感得分: {dict_analysis['overall_score']:+.4f}\")\n", "    print(f\"    - 情感倾向: {dict_analysis['sentiment']}\")\n", "    print(f\"    - 匹配情感词: {dict_analysis['matched_words_count']} 个\")\n", "    \n", "    print(f\"  • FinBERT方法:\")\n", "    print(f\"    - 情感得分: {finbert_analysis['overall_score']:+.4f}\")\n", "    print(f\"    - 情感倾向: {finbert_analysis['sentiment']}\")\n", "    \n", "    print(f\"  • 综合评估:\")\n", "    print(f\"    - 综合得分: {final_assessment['combined_score']:+.4f}\")\n", "    print(f\"    - 综合倾向: {final_assessment['combined_sentiment']}\")\n", "    print(f\"    - 方法一致率: {final_assessment['agreement_rate']:.1%}\")\n", "    print(f\"    - 结果可信度: {final_assessment['confidence']}\")\n", "    \n", "    # 情感解读\n", "    print(f\"\\n💡 结果解读:\")\n", "    combined_score = final_assessment['combined_score']\n", "    agreement_rate = final_assessment['agreement_rate']\n", "    \n", "    if combined_score > 0.2:\n", "        sentiment_desc = \"研报整体呈现积极正面的情感倾向\"\n", "    elif combined_score < -0.2:\n", "        sentiment_desc = \"研报整体呈现消极负面的情感倾向\"\n", "    else:\n", "        sentiment_desc = \"研报整体情感倾向相对中性\"\n", "    \n", "    if agreement_rate > 0.7:\n", "        confidence_desc = \"两种分析方法高度一致，结果可信度很高\"\n", "    elif agreement_rate > 0.4:\n", "        confidence_desc = \"两种分析方法基本一致，结果具有一定可信度\"\n", "    else:\n", "        confidence_desc = \"两种分析方法存在较大分歧，建议进一步人工审核\"\n", "    \n", "    print(f\"  • {sentiment_desc}\")\n", "    print(f\"  • {confidence_desc}\")\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "\n", "print(\"✅ 主流程模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 示例运行和结果展示"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.1 设置文件路径\n", "\n", "请根据您的实际文件路径修改以下设置："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置文件路径 - 请根据实际情况修改\n", "PDF_PATH = \"data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\"\n", "POSITIVE_DICT_PATH = \"data/CFSD中文金融情感词典/正面词典.csv\"\n", "NEGATIVE_DICT_PATH = \"data/CFSD中文金融情感词典/负面词典.csv\"\n", "STOPWORDS_PATH = \"data/stopwords.txt\"\n", "\n", "# 检查文件是否存在\n", "files_to_check = {\n", "    \"PDF文件\": PDF_PATH,\n", "    \"正面词典\": POSITIVE_DICT_PATH,\n", "    \"负面词典\": NEGATIVE_DICT_PATH,\n", "    \"停用词文件\": STOPWORDS_PATH\n", "}\n", "\n", "print(\"📁 检查文件存在性:\")\n", "for name, path in files_to_check.items():\n", "    exists = os.path.exists(path)\n", "    status = \"✅\" if exists else \"❌\"\n", "    print(f\"  {status} {name}: {path}\")\n", "    if not exists:\n", "        print(f\"      请确保文件存在或修改路径\")\n", "\n", "# 如果PDF文件不存在，尝试查找其他PDF文件\n", "if not os.path.exists(PDF_PATH):\n", "    print(\"\\n🔍 尝试查找其他PDF文件...\")\n", "    data_dir = \"data\"\n", "    if os.path.exists(data_dir):\n", "        pdf_files = [f for f in os.listdir(data_dir) if f.endswith('.pdf')]\n", "        if pdf_files:\n", "            PDF_PATH = os.path.join(data_dir, pdf_files[0])\n", "            print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "        else:\n", "            print(\"❌ 未找到任何PDF文件\")\n", "    else:\n", "        print(\"❌ data目录不存在\")\n", "\n", "print(f\"\\n🎯 将使用以下文件进行分析:\")\n", "print(f\"  📄 PDF文件: {PDF_PATH}\")\n", "print(f\"  📚 正面词典: {POSITIVE_DICT_PATH}\")\n", "print(f\"  📚 负面词典: {NEGATIVE_DICT_PATH}\")\n", "print(f\"  🚫 停用词: {STOPWORDS_PATH}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2 执行完整分析流程"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 执行完整的研报情感分析\n", "if os.path.exists(PDF_PATH):\n", "    print(\"🚀 开始执行研报情感分析...\")\n", "    \n", "    # 执行分析\n", "    analysis_results = analyze_financial_report(\n", "        pdf_path=PDF_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        num_keywords=20\n", "    )\n", "    \n", "    # 显示结果摘要\n", "    display_analysis_results(analysis_results)\n", "    \n", "else:\n", "    print(\"❌ PDF文件不存在，无法执行分析\")\n", "    print(\"请确保PDF文件路径正确，或将PDF文件放在data目录下\")\n", "    analysis_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.3 详细结果展示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示详细的分析结果\n", "if analysis_results and analysis_results['success']:\n", "    print(\"📋 详细分析结果:\")\n", "    print(\"=\" * 80)\n", "    \n", "    # 1. 文本样本\n", "    print(\"\\n📄 提取的文本样本 (前500字符):\")\n", "    print(\"-\" * 50)\n", "    print(analysis_results['text_sample'])\n", "    \n", "    # 2. 关键词及其代表性句子\n", "    print(\"\\n🔍 关键词及其代表性句子:\")\n", "    print(\"-\" * 50)\n", "    keyword_sentences = analysis_results['keyword_sentences']\n", "    \n", "    for i, (keyword, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "        print(f\"\\n{i:2d}. 关键词: {keyword} (权重: {weight:.4f})\")\n", "        sentences = keyword_sentences.get(keyword, [])\n", "        if sentences:\n", "            for j, sentence in enumerate(sentences[:2], 1):\n", "                print(f\"    句子{j}: {sentence[:100]}{'...' if len(sentence) > 100 else ''}\")\n", "        else:\n", "            print(\"    (未找到包含该关键词的句子)\")\n", "    \n", "    # 3. 匹配的情感词\n", "    print(\"\\n📊 匹配的情感词 (前20个):\")\n", "    print(\"-\" * 50)\n", "    matched_words = analysis_results['dict_analysis']['matched_words']\n", "    \n", "    if matched_words:\n", "        # 按情感得分排序\n", "        sorted_sentiment_words = sorted(matched_words, key=lambda x: abs(x[1]), reverse=True)[:20]\n", "        \n", "        positive_words = [(w, s) for w, s in sorted_sentiment_words if s > 0]\n", "        negative_words = [(w, s) for w, s in sorted_sentiment_words if s < 0]\n", "        \n", "        print(f\"正面情感词 ({len(positive_words)} 个):\")\n", "        for word, score in positive_words[:10]:\n", "            print(f\"  • {word} ({score:+.2f})\")\n", "        \n", "        print(f\"\\n负面情感词 ({len(negative_words)} 个):\")\n", "        for word, score in negative_words[:10]:\n", "            print(f\"  • {word} ({score:+.2f})\")\n", "    else:\n", "        print(\"未匹配到情感词\")\n", "    \n", "    # 4. 比较分析结果表格\n", "    print(\"\\n🔄 两种方法比较结果 (前15个关键词):\")\n", "    print(\"-\" * 50)\n", "    comparison_df = analysis_results['comparison']['comparison_df']\n", "    \n", "    if not comparison_df.empty:\n", "        # 按权重排序显示前15个\n", "        top_comparison = comparison_df.nlargest(15, '权重')\n", "        \n", "        print(f\"{'关键词':<12} {'词典得分':<8} {'词典倾向':<6} {'FinBERT得分':<10} {'FinBERT倾向':<8} {'一致':<4} {'权重':<8}\")\n", "        print(\"-\" * 70)\n", "        \n", "        for _, row in top_comparison.iterrows():\n", "            consistent = \"✅\" if row['倾向一致'] else \"❌\"\n", "            print(f\"{row['关键词']:<12} {row['词典情感得分']:>7.3f} {row['词典情感倾向']:<6} {row['FinBERT情感得分']:>9.3f} {row['FinBERT情感倾向']:<8} {consistent:<4} {row['权重']:>7.4f}\")\n", "    \n", "    print(\"\\n\" + \"=\" * 80)\n", "else:\n", "    print(\"❌ 无法显示详细结果，分析未成功完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.4 可视化结果展示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成可视化图表\n", "if analysis_results and analysis_results['success']:\n", "    print(\"📊 生成可视化图表...\")\n", "    \n", "    # 获取分析结果\n", "    dict_score = analysis_results['dict_analysis']['overall_score']\n", "    finbert_score = analysis_results['finbert_analysis']['overall_score']\n", "    dict_keywords = analysis_results['dict_analysis']['keyword_scores']\n", "    finbert_keywords = analysis_results['finbert_analysis']['keyword_scores']\n", "    comparison_df = analysis_results['comparison']['comparison_df']\n", "    keywords = analysis_results['keywords']\n", "    \n", "    try:\n", "        # 1. 情感分析方法对比图\n", "        print(\"\\n📈 1. 情感分析方法对比\")\n", "        create_sentiment_comparison_chart(dict_score, finbert_score)\n", "        \n", "        # 2. 词典方法关键词情感分布\n", "        if dict_keywords:\n", "            print(\"\\n📊 2. 词典方法 - 关键词情感分布\")\n", "            create_keyword_sentiment_chart(dict_keywords, \"Dictionary Method\", top_n=15)\n", "        \n", "        # 3. FinBERT方法关键词情感分布\n", "        if finbert_keywords:\n", "            print(\"\\n🤖 3. FinBERT方法 - 关键词情感分布\")\n", "            create_keyword_sentiment_chart(finbert_keywords, \"FinBERT Method\", top_n=15)\n", "        \n", "        # 4. 两种方法一致性分析\n", "        if not comparison_df.empty:\n", "            print(\"\\n🔄 4. 两种方法一致性分析\")\n", "            create_agreement_chart(comparison_df)\n", "        \n", "        # 5. 关键词云图\n", "        if keywords:\n", "            print(\"\\n☁️ 5. 关键词云图\")\n", "            create_wordcloud(keywords)\n", "        \n", "        print(\"\\n✅ 所有可视化图表生成完成!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n⚠️ 可视化过程中出现错误: {e}\")\n", "        print(\"这可能是由于缺少某些可视化库或字体问题导致的\")\n", "        \n", "else:\n", "    print(\"❌ 无法生成可视化图表，分析未成功完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.5 结果数据导出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将分析结果导出为DataFrame，便于进一步分析\n", "if analysis_results and analysis_results['success']:\n", "    print(\"💾 导出分析结果数据...\")\n", "    \n", "    # 1. 关键词分析结果\n", "    keywords_data = []\n", "    for keyword, weight in analysis_results['keywords']:\n", "        # 获取词典和FinBERT的情感得分\n", "        dict_score = 0\n", "        finbert_score = 0\n", "        \n", "        # 从词典分析结果中查找\n", "        for kw, score, w in analysis_results['dict_analysis']['keyword_scores']:\n", "            if kw == keyword:\n", "                dict_score = score\n", "                break\n", "        \n", "        # 从FinBERT分析结果中查找\n", "        for kw, score, w in analysis_results['finbert_analysis']['keyword_scores']:\n", "            if kw == keyword:\n", "                finbert_score = score\n", "                break\n", "        \n", "        keywords_data.append({\n", "            '关键词': keyword,\n", "            '权重': weight,\n", "            '词典情感得分': dict_score,\n", "            'FinBERT情感得分': finbert_score,\n", "            '平均情感得分': (dict_score + finbert_score) / 2,\n", "            '得分差异': abs(dict_score - finbert_score)\n", "        })\n", "    \n", "    keywords_df = pd.DataFrame(keywords_data)\n", "    \n", "    print(\"\\n📋 关键词分析结果表:\")\n", "    print(keywords_df.head(10).to_string(index=False, float_format='%.4f'))\n", "    \n", "    # 2. 情感词统计\n", "    matched_words = analysis_results['dict_analysis']['matched_words']\n", "    if matched_words:\n", "        sentiment_stats = {\n", "            '正面情感词数量': len([w for w, s in matched_words if s > 0]),\n", "            '负面情感词数量': len([w for w, s in matched_words if s < 0]),\n", "            '总情感词数量': len(matched_words),\n", "            '正面情感词平均得分': np.mean([s for w, s in matched_words if s > 0]) if any(s > 0 for w, s in matched_words) else 0,\n", "            '负面情感词平均得分': np.mean([s for w, s in matched_words if s < 0]) if any(s < 0 for w, s in matched_words) else 0\n", "        }\n", "        \n", "        print(\"\\n📊 情感词统计:\")\n", "        for key, value in sentiment_stats.items():\n", "            if '得分' in key:\n", "                print(f\"  • {key}: {value:+.4f}\")\n", "            else:\n", "                print(f\"  • {key}: {value}\")\n", "    \n", "    # 3. 分析摘要\n", "    print(\"\\n📈 分析摘要:\")\n", "    final_assessment = analysis_results['final_assessment']\n", "    print(f\"  • 综合情感得分: {final_assessment['combined_score']:+.4f}\")\n", "    print(f\"  • 综合情感倾向: {final_assessment['combined_sentiment']}\")\n", "    print(f\"  • 方法一致率: {final_assessment['agreement_rate']:.1%}\")\n", "    print(f\"  • 结果可信度: {final_assessment['confidence']}\")\n", "    \n", "    print(\"\\n✅ 数据导出完成!\")\n", "    \n", "else:\n", "    print(\"❌ 无法导出数据，分析未成功完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 总结和说明\n", "\n", "### 9.1 系统功能总结\n", "\n", "本研报情感分析系统具备以下完整功能：\n", "\n", "1. **PDF文本提取**: 使用多种方法（PyMuPDF、pdfplumber等）确保文本提取的完整性\n", "2. **文本预处理**: 智能分词、停用词过滤、文本清洗\n", "3. **关键词提取**: 结合TextRank、TF-IDF等多种算法提取重要关键词\n", "4. **情感分析**: \n", "   - 基于金融情感词典的传统方法\n", "   - 基于FinBERT的深度学习方法\n", "   - 两种方法的结果比较和综合评估\n", "5. **可视化展示**: 多种图表直观展示分析结果\n", "6. **结果解读**: 提供详细的分析解读和建议\n", "\n", "### 9.2 使用说明\n", "\n", "1. **环境要求**: 确保安装了所需的Python库（pandas, matplotlib, jieba, transformers等）\n", "2. **文件准备**: \n", "   - 将待分析的PDF文件放在`data`目录下\n", "   - 确保情感词典文件存在\n", "   - 准备停用词文件\n", "3. **运行方式**: 按顺序执行notebook中的所有单元格\n", "4. **结果查看**: 分析结果会直接在notebook中显示，包括文字摘要和可视化图表\n", "\n", "### 9.3 技术特点\n", "\n", "- **稳定性**: 多重错误处理和备选方案确保系统稳定运行\n", "- **准确性**: 结合传统方法和深度学习方法提高分析准确性\n", "- **可解释性**: 详细的分析过程和结果解读\n", "- **易用性**: 一键运行，结果直观展示\n", "- **兼容性**: 使用系统默认字体，避免字体显示问题\n", "\n", "### 9.4 注意事项\n", "\n", "1. 首次运行可能需要下载FinBERT模型，请确保网络连接正常\n", "2. 如果某些库不可用，系统会自动使用备选方案\n", "3. 分析结果仅供参考，建议结合人工判断进行最终决策\n", "4. 对于特别重要的分析，建议多次运行以确保结果稳定性\n", "\n", "---\n", "\n", "**🎉 恭喜！您已完成研报情感分析系统的完整流程！**\n", "\n", "如有任何问题或需要进一步定制，请随时联系开发团队。"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}