"""
关键词提取模块
使用TextRank算法提取文本中的关键词
支持多种关键词提取方法，包括TextRank和TF-IDF
"""

from textrank4zh import TextRank4Keyword
import jieba
import jieba.analyse
import time
import numpy as np
from collections import defaultdict
import re
import os

# 添加金融领域常用词汇，提高关键词质量
def add_finance_terms_to_jieba():
    """向jieba分词器添加金融领域常用词汇"""
    finance_terms = [
        "金融科技", "金融IT", "证券", "银行", "保险", "基金", "信托", "期货", "期权",
        "股票", "债券", "理财", "投资", "风险", "收益", "资产", "负债", "权益",
        "流动性", "杠杆", "融资", "融券", "做市", "交易", "结算", "清算", "托管",
        "估值", "风控", "合规", "监管", "审计", "会计", "财务", "税务", "法律",
        "咨询", "研究", "分析", "策略", "模型", "算法", "数据", "信息", "系统",
        "平台", "架构", "开发", "测试", "运维", "安全", "云计算", "大数据", "人工智能",
        "区块链", "物联网", "移动互联", "互联网金融", "普惠金融", "供应链金融", "消费金融",
        "财富管理", "资产管理", "投资银行", "商业银行", "投资顾问", "证券公司", "基金公司",
        "保险公司", "信托公司", "期货公司", "私募基金", "公募基金", "对冲基金", "创投基金",
        "股权投资", "并购重组", "IPO", "定增", "可转债", "ABS", "ETF", "LOF", "QDII",
        "沪深300", "中证500", "上证50", "创业板", "科创板", "新三板", "北交所"
    ]

    # 添加金融词汇到jieba词典
    for term in finance_terms:
        jieba.add_word(term, freq=20000)

    # 尝试加载外部金融词典
    try:
        finance_dict_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                         'data', '金融词汇字典.txt')
        if os.path.exists(finance_dict_path):
            jieba.load_userdict(finance_dict_path)
            print(f"成功加载金融词典: {finance_dict_path}")
    except Exception as e:
        print(f"加载金融词典时出错: {e}")

# 在模块加载时添加金融词汇
add_finance_terms_to_jieba()


def extract_keywords_textrank(text, num_keywords=20, word_min_len=2, method='auto', filter_numbers=True):
    """
    使用TextRank算法提取文本中的关键词

    参数:
        text (str): 待提取关键词的文本
        num_keywords (int): 提取的关键词数量
        word_min_len (int): 关键词的最小长度
        method (str): 使用的方法，可选值为'auto'、'textrank4zh'、'jieba'、'tfidf'、'all'
        filter_numbers (bool): 是否过滤纯数字和代码

    返回:
        list: 关键词列表，每个元素为(word, weight)元组
    """
    start_time = time.time()

    # 确保文本不为空
    if not text or len(text.strip()) < word_min_len * 3:
        print("文本为空或过短，无法提取关键词")
        return []

    # 不需要在这里调用，我们将在模块级别添加金融词汇

    # 根据指定方法提取关键词
    if method == 'auto':
        # 自动选择最佳方法
        try:
            # 首先尝试使用TextRank4Keyword
            result = _extract_by_textrank4zh(text, num_keywords, word_min_len)
            if result:
                end_time = time.time()
                print(f"使用TextRank4Keyword提取关键词成功，耗时: {end_time - start_time:.2f} 秒")
                return result

            # 如果失败，尝试使用jieba的TextRank
            print("TextRank4Keyword返回空结果，尝试使用jieba的TextRank...")
            result = _extract_by_jieba_textrank(text, num_keywords)
            if result:
                end_time = time.time()
                print(f"使用jieba的TextRank提取关键词成功，耗时: {end_time - start_time:.2f} 秒")
                return result

            # 如果仍然失败，尝试使用TF-IDF
            print("jieba的TextRank返回空结果，尝试使用TF-IDF...")
            result = _extract_by_tfidf(text, num_keywords)
            end_time = time.time()
            print(f"使用TF-IDF提取关键词成功，耗时: {end_time - start_time:.2f} 秒")
            return result

        except Exception as e:
            print(f"自动提取关键词时出错: {e}")
            return _get_fallback_keywords(num_keywords)

    elif method == 'textrank4zh':
        try:
            result = _extract_by_textrank4zh(text, num_keywords, word_min_len)
            end_time = time.time()
            print(f"使用TextRank4Keyword提取关键词，耗时: {end_time - start_time:.2f} 秒")
            return result
        except Exception as e:
            print(f"使用TextRank4Keyword提取关键词时出错: {e}")
            return _get_fallback_keywords(num_keywords)

    elif method == 'jieba':
        try:
            result = _extract_by_jieba_textrank(text, num_keywords)
            end_time = time.time()
            print(f"使用jieba的TextRank提取关键词，耗时: {end_time - start_time:.2f} 秒")
            return result
        except Exception as e:
            print(f"使用jieba的TextRank提取关键词时出错: {e}")
            return _get_fallback_keywords(num_keywords)

    elif method == 'tfidf':
        try:
            result = _extract_by_tfidf(text, num_keywords)
            end_time = time.time()
            print(f"使用TF-IDF提取关键词，耗时: {end_time - start_time:.2f} 秒")
            return result
        except Exception as e:
            print(f"使用TF-IDF提取关键词时出错: {e}")
            return _get_fallback_keywords(num_keywords)

    elif method == 'all':
        # 使用所有方法并合并结果
        all_keywords = {}

        try:
            # TextRank4Keyword
            tr4w_keywords = _extract_by_textrank4zh(text, num_keywords * 2, word_min_len)
            for word, weight in tr4w_keywords:
                if word in all_keywords:
                    all_keywords[word] += weight
                else:
                    all_keywords[word] = weight
        except Exception as e:
            print(f"使用TextRank4Keyword提取关键词时出错: {e}")

        try:
            # jieba的TextRank
            jieba_keywords = _extract_by_jieba_textrank(text, num_keywords * 2)
            for word, weight in jieba_keywords:
                if word in all_keywords:
                    all_keywords[word] += weight
                else:
                    all_keywords[word] = weight
        except Exception as e:
            print(f"使用jieba的TextRank提取关键词时出错: {e}")

        try:
            # TF-IDF
            tfidf_keywords = _extract_by_tfidf(text, num_keywords * 2)
            for word, weight in tfidf_keywords:
                if word in all_keywords:
                    all_keywords[word] += weight
                else:
                    all_keywords[word] = weight
        except Exception as e:
            print(f"使用TF-IDF提取关键词时出错: {e}")

        # 如果没有提取到关键词，返回备选关键词
        if not all_keywords:
            return _get_fallback_keywords(num_keywords)

        # 按权重排序并返回前num_keywords个关键词
        sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)
        result = sorted_keywords[:num_keywords]

        end_time = time.time()
        print(f"使用所有方法提取关键词，耗时: {end_time - start_time:.2f} 秒")
        return result

    else:
        print(f"不支持的方法: {method}，使用自动方法代替")
        return extract_keywords_textrank(text, num_keywords, word_min_len, method='auto')


def _extract_by_textrank4zh(text, num_keywords, word_min_len):
    """
    使用TextRank4Keyword提取关键词
    优化处理金融研报文本，提高关键词提取质量
    """
    try:
        print("使用TextRank4Keyword提取关键词...")

        # 创建TextRank4Keyword实例
        tr4w = TextRank4Keyword()

        # 使用更大的窗口大小，更好地捕捉金融术语的上下文关系
        # 对于金融研报，窗口大小为3-4效果更好
        window_size = 4

        # 分析文本
        tr4w.analyze(text=text, lower=True, window=window_size)

        # 获取更多关键词，以便后续过滤
        raw_keywords = tr4w.get_keywords(num_keywords * 3, word_min_len=word_min_len)

        print(f"TextRank4Keyword原始提取: {len(raw_keywords)} 个关键词")

        # 过滤数字和代码，优化金融关键词
        filtered_keywords = []

        # 金融领域重要词汇，即使权重较低也应保留
        financial_important_terms = {
            "金融", "证券", "银行", "保险", "基金", "信托", "期货", "期权", "股票", "债券",
            "投资", "风险", "收益", "资产", "负债", "权益", "流动性", "杠杆", "融资", "交易",
            "结算", "清算", "托管", "估值", "风控", "合规", "监管", "审计", "财务", "税务",
            "咨询", "研究", "分析", "策略", "模型", "算法", "数据", "系统", "平台", "架构",
            "金融科技", "金融IT", "金融服务", "金融产品", "金融机构", "金融市场", "金融监管"
        }

        # 优先处理金融重要词汇
        important_keywords = []
        normal_keywords = []

        for keyword in raw_keywords:
            # 跳过纯数字
            if keyword.word.isdigit():
                continue

            # 跳过主要是数字的关键词
            if sum(c.isdigit() for c in keyword.word) > len(keyword.word) / 2:
                continue

            # 跳过常见的代码标识符和无意义词汇
            if keyword.word.lower() in ['cid', 'id', 'div', 'span', 'href', 'src', 'alt', 'url', 'http', 'https',
                                       'com', 'cn', 'www', 'html', 'htm', 'pdf', 'doc', 'docx', 'xls', 'xlsx']:
                continue

            # 跳过过短的词（通常没有意义）
            if len(keyword.word) < word_min_len:
                continue

            # 跳过包含特殊字符的词
            if re.search(r'[^\u4e00-\u9fa5a-zA-Z0-9]', keyword.word):
                continue

            # 检查是否是金融重要词汇
            if keyword.word in financial_important_terms:
                important_keywords.append((keyword.word, keyword.weight * 1.5))  # 提高重要词汇的权重
            else:
                normal_keywords.append((keyword.word, keyword.weight))

        # 合并重要词汇和普通词汇，确保重要词汇优先
        all_keywords = important_keywords + normal_keywords

        # 去除重复词汇（可能由于权重调整导致）
        unique_keywords = []
        seen_words = set()

        for word, weight in all_keywords:
            if word not in seen_words:
                unique_keywords.append((word, weight))
                seen_words.add(word)

                # 如果已经有足够的关键词，就停止
                if len(unique_keywords) >= num_keywords:
                    break

        # 如果过滤后没有足够的关键词，返回原始关键词
        if len(unique_keywords) < max(3, num_keywords / 3):
            print(f"过滤后关键词不足，使用原始关键词")
            return [(keyword.word, keyword.weight) for keyword in raw_keywords[:num_keywords]]

        print(f"TextRank4Keyword过滤后: {len(unique_keywords)} 个关键词")
        # 返回过滤后的关键词和权重
        return unique_keywords

    except AttributeError as e:
        if 'from_numpy_matrix' in str(e):
            # 这是 NetworkX 版本不兼容导致的问题
            print("NetworkX 版本不兼容，尝试使用替代方法...")
            # 使用 jieba 的 TextRank 作为替代
            return _extract_by_jieba_textrank(text, num_keywords)
        else:
            # 其他 AttributeError，重新抛出
            print(f"TextRank4Keyword AttributeError: {e}")
            # 使用 jieba 的 TextRank 作为替代
            return _extract_by_jieba_textrank(text, num_keywords)
    except Exception as e:
        print(f"TextRank4Keyword 提取失败: {e}")
        # 使用 jieba 的 TextRank 作为替代
        return _extract_by_jieba_textrank(text, num_keywords)


def _extract_by_jieba_textrank(text, num_keywords):
    """使用jieba的TextRank提取关键词"""
    # 提取更多关键词，以便过滤后仍有足够的关键词
    keywords = jieba.analyse.textrank(text, topK=num_keywords * 2, withWeight=True)

    # 过滤数字和代码
    filtered_keywords = []
    for word, weight in keywords:
        # 跳过纯数字
        if word.isdigit():
            continue

        # 跳过主要是数字的关键词
        if sum(c.isdigit() for c in word) > len(word) / 2:
            continue

        # 跳过常见的代码标识符
        if word.lower() in ['cid', 'id', 'div', 'span', 'href', 'src', 'alt', 'url', 'http', 'https']:
            continue

        # 跳过过短的词（通常没有意义）
        if len(word) < 2:
            continue

        # 跳过包含特殊字符的词
        if re.search(r'[^\u4e00-\u9fa5a-zA-Z0-9]', word):
            continue

        filtered_keywords.append((word, weight))

        # 如果已经有足够的关键词，就停止
        if len(filtered_keywords) >= num_keywords:
            break

    # 如果过滤后没有足够的关键词，返回原始关键词
    if len(filtered_keywords) < num_keywords / 2:
        return keywords[:num_keywords]

    return filtered_keywords


def _extract_by_tfidf(text, num_keywords):
    """使用TF-IDF提取关键词"""
    # 提取更多关键词，以便过滤后仍有足够的关键词
    keywords = jieba.analyse.extract_tags(text, topK=num_keywords * 2, withWeight=True)

    # 过滤数字和代码
    filtered_keywords = []
    for word, weight in keywords:
        # 跳过纯数字
        if word.isdigit():
            continue

        # 跳过主要是数字的关键词
        if sum(c.isdigit() for c in word) > len(word) / 2:
            continue

        # 跳过常见的代码标识符
        if word.lower() in ['cid', 'id', 'div', 'span', 'href', 'src', 'alt', 'url', 'http', 'https']:
            continue

        # 跳过过短的词（通常没有意义）
        if len(word) < 2:
            continue

        # 跳过包含特殊字符的词
        if re.search(r'[^\u4e00-\u9fa5a-zA-Z0-9]', word):
            continue

        filtered_keywords.append((word, weight))

        # 如果已经有足够的关键词，就停止
        if len(filtered_keywords) >= num_keywords:
            break

    # 如果过滤后没有足够的关键词，返回原始关键词
    if len(filtered_keywords) < num_keywords / 2:
        return keywords[:num_keywords]

    return filtered_keywords


def _get_fallback_keywords(num_keywords):
    """获取备选关键词"""
    fallback_keywords = [
        ("金融", 1.0), ("投资", 0.9), ("市场", 0.8), ("风险", 0.7),
        ("收益", 0.6), ("分析", 0.5), ("策略", 0.4), ("行业", 0.3),
        ("证券", 0.75), ("银行", 0.65), ("保险", 0.55), ("基金", 0.45),
        ("股票", 0.85), ("债券", 0.75), ("期货", 0.65), ("期权", 0.55)
    ]
    print(f"使用备选关键词: {[k[0] for k in fallback_keywords[:num_keywords]]}")
    return fallback_keywords[:num_keywords]


def extract_keywords_jieba(text, num_keywords=20):
    """
    使用jieba的TextRank算法提取文本中的关键词

    参数:
        text (str): 待提取关键词的文本
        num_keywords (int): 提取的关键词数量

    返回:
        list: 关键词列表，每个元素为(word, weight)元组
    """
    try:
        # 使用jieba的TextRank算法提取关键词
        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)
        return keywords

    except Exception as e:
        print(f"使用jieba提取关键词时出错: {e}")
        return []


def extract_keywords_with_sentences(text, keywords, num_sentences=3):
    """
    提取包含关键词的代表性句子，优化金融研报文本处理

    参数:
        text (str): 原始文本
        keywords (list): 关键词列表，每个元素为(word, weight)元组
        num_sentences (int): 每个关键词返回的句子数量

    返回:
        dict: 关键词及其对应的代表性句子
    """
    try:
        print(f"为{len(keywords)}个关键词提取代表性句子...")

        # 使用更复杂的句子分割规则，处理金融研报中的各种句子结构
        sentences = []
        # 使用多种标点符号分割句子
        raw_sentences = re.split(r'([。！？!?；;])', text)

        # 重新组合句子和标点符号
        i = 0
        while i < len(raw_sentences) - 1:
            if i + 1 < len(raw_sentences):
                sentences.append(raw_sentences[i] + raw_sentences[i+1])
                i += 2
            else:
                sentences.append(raw_sentences[i])
                i += 1

        # 过滤空句子和过短的句子
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]

        print(f"从文本中提取出 {len(sentences)} 个句子")

        # 为每个关键词找到包含它的最相关句子
        keyword_sentences = {}

        for keyword, weight in keywords:
            # 初始化关键词的句子列表
            keyword_sentences[keyword] = []

            # 找到包含关键词的所有句子
            matching_sentences = []

            for sentence in sentences:
                if keyword in sentence:
                    # 计算句子的相关性得分
                    # 1. 句子长度适中（不太长也不太短）
                    length_score = 1.0
                    if len(sentence) < 20:
                        length_score = 0.7
                    elif len(sentence) > 100:
                        length_score = 0.8

                    # 2. 关键词在句子中出现的次数
                    keyword_count = sentence.count(keyword)
                    count_score = min(1.0, 0.5 + 0.25 * keyword_count)

                    # 3. 句子中包含其他关键词的数量
                    other_keywords_count = sum(1 for kw, _ in keywords if kw != keyword and kw in sentence)
                    other_kw_score = min(1.0, 0.6 + 0.1 * other_keywords_count)

                    # 4. 关键词在句子中的位置（靠前的句子更重要）
                    position = sentence.find(keyword)
                    position_score = 1.0 - (position / len(sentence)) * 0.5

                    # 综合得分
                    relevance_score = (length_score * 0.2 +
                                      count_score * 0.3 +
                                      other_kw_score * 0.3 +
                                      position_score * 0.2)

                    matching_sentences.append((sentence, relevance_score))

            # 按相关性得分排序
            matching_sentences.sort(key=lambda x: x[1], reverse=True)

            # 选择前num_sentences个最相关的句子
            selected_sentences = [s for s, _ in matching_sentences[:num_sentences]]

            # 如果没有找到足够的句子，可能是因为关键词在表格中
            if len(selected_sentences) < num_sentences:
                # 尝试在表格文本中查找
                table_pattern = r'表格\d+（第\d+页）:.*?(' + re.escape(keyword) + r').*?(?=表格\d+（第\d+页）:|$)'
                table_matches = re.findall(table_pattern, text, re.DOTALL)

                if table_matches:
                    for match in table_matches:
                        # 提取包含关键词的表格行
                        table_line = re.search(r'.*' + re.escape(keyword) + r'.*', match)
                        if table_line and len(selected_sentences) < num_sentences:
                            selected_sentences.append(f"[表格] {table_line.group(0)}")

            # 保存结果
            keyword_sentences[keyword] = selected_sentences

            # 如果没有找到任何句子，添加一个提示
            if not selected_sentences:
                keyword_sentences[keyword] = ["未找到包含该关键词的代表性句子"]

        print(f"成功为{len(keyword_sentences)}个关键词提取代表性句子")
        return keyword_sentences

    except Exception as e:
        print(f"提取关键词句子时出错: {e}")
        return {}
